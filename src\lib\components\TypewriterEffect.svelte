<script lang="ts">
  import { onMount } from 'svelte';
  
  export let text: string = '';
  export let speed: number = 100; // 打字速度 (毫秒)
  export let delay: number = 0; // 开始延迟
  export let showCursor: boolean = true; // 是否显示光标
  export let cursorChar: string = '|'; // 光标字符
  export let loop: boolean = false; // 是否循环
  export let deleteSpeed: number = 50; // 删除速度
  export let pauseTime: number = 2000; // 暂停时间
  
  let displayText = '';
  let currentIndex = 0;
  let isDeleting = false;
  let isComplete = false;
  let timeoutId: number;
  
  function typeWriter() {
    if (!isDeleting && currentIndex < text.length) {
      // 正在打字
      displayText = text.slice(0, currentIndex + 1);
      currentIndex++;
      timeoutId = setTimeout(typeWriter, speed + Math.random() * 50); // 添加随机性
    } else if (!isDeleting && currentIndex === text.length) {
      // 打字完成
      isComplete = true;
      if (loop) {
        timeoutId = setTimeout(() => {
          isDeleting = true;
          typeWriter();
        }, pauseTime);
      }
    } else if (isDeleting && currentIndex > 0) {
      // 正在删除
      currentIndex--;
      displayText = text.slice(0, currentIndex);
      timeoutId = setTimeout(typeWriter, deleteSpeed);
    } else if (isDeleting && currentIndex === 0) {
      // 删除完成，重新开始
      isDeleting = false;
      isComplete = false;
      timeoutId = setTimeout(typeWriter, speed);
    }
  }
  
  onMount(() => {
    if (text) {
      timeoutId = setTimeout(() => {
        typeWriter();
      }, delay);
    }
    
    return () => {
      clearTimeout(timeoutId);
    };
  });
  
  // 响应式更新文本
  $: if (text) {
    clearTimeout(timeoutId);
    displayText = '';
    currentIndex = 0;
    isDeleting = false;
    isComplete = false;
    timeoutId = setTimeout(() => {
      typeWriter();
    }, delay);
  }
</script>

<span class="typewriter-text">
  {displayText}<span class="cursor" class:show={showCursor && !isComplete}>{cursorChar}</span>
</span>

<style>
  .typewriter-text {
    display: inline-block;
  }
  
  .cursor {
    opacity: 0;
    animation: blink 1s infinite;
    color: inherit;
    font-weight: inherit;
  }
  
  .cursor.show {
    opacity: 1;
  }
  
  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }
</style>
