<script lang="ts">
  import { onMount } from 'svelte';
  import ParticlesBackground from '$lib/components/ParticlesBackground.svelte';
  import ImageWithFallback from '$lib/components/ImageWithFallback.svelte';
  import { siteConfig } from '$lib/config.js';

  // 获取错误状态码，默认为404
  let errorStatus = 404;

  let mainContainer: HTMLElement;

  function goBack() {
    history.back();
  }

  onMount(() => {
    // 动态设置背景图片，如果失败则使用渐变背景
    if (mainContainer) {
      const img = new Image();
      img.onload = () => {
        mainContainer.style.backgroundImage = `url('${siteConfig.background.image}')`;
      };
      img.onerror = () => {
        mainContainer.style.background = siteConfig.background.fallbackColor;
      };
      img.src = siteConfig.background.image;
    }
  });
</script>

<svelte:head>
  <title>页面未找到 - {errorStatus}</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
</svelte:head>

<main bind:this={mainContainer} class="error-page">
  <ParticlesBackground />
  <div class="background-overlay"></div>

  <div class="error-content">
    <div class="error-card">
      <!-- Logo区域 -->
      <div class="error-logo">
        <ImageWithFallback src="/images/logo.gif" alt="Logo" fallbackText="404" />
      </div>

      <!-- 错误信息 -->
      <div class="error-header">
        <div class="error-code">{errorStatus}</div>
        <h1>页面走丢了</h1>
        <p class="error-subtitle">您请求的页面不存在、或已被删除、或暂时不可用</p>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <a href="/" class="back-link primary">
          <i class="fa-solid fa-house"></i>
          返回首页
        </a>
        <button on:click={goBack} class="back-link secondary">
          <i class="fa-solid fa-arrow-left"></i>
          返回上页
        </button>
      </div>

      <!-- 装饰性社交图标 -->
      <div class="error-social">
        <a href="https://blog.nmslwsnd.com" target="_blank" class="social-icon" title="Blog" aria-label="Blog">
          <i class="fa-solid fa-user"></i>
        </a>
        <a href="https://nextcloud.nmslwsnd.com" target="_blank" class="social-icon" title="NextCloud" aria-label="NextCloud">
          <i class="fa-solid fa-cloud"></i>
        </a>
        <a href="https://steamcommunity.com/id/hello-world-" target="_blank" class="social-icon" title="Steam" aria-label="Steam">
          <i class="fa-brands fa-steam"></i>
        </a>
        <a href="https://github.com/limitcool" target="_blank" class="social-icon" title="GitHub" aria-label="GitHub">
          <i class="fa-brands fa-github"></i>
        </a>
        <a href="https://space.bilibili.com/your-bilibili-id" target="_blank" class="social-icon" title="Bilibili" aria-label="Bilibili">
          <i class="fa-brands fa-bilibili"></i>
        </a>
      </div>
    </div>
  </div>
</main>

<style>
  .error-page {
    position: relative;
    min-height: 100vh;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
  }

  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 0;
  }

  .error-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    width: 100%;
  }

  .error-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 50px 40px;
    text-align: center;
    backdrop-filter: blur(20px);
    animation: fadeInUp 0.8s ease-out;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  /* Logo样式 */
  .error-logo {
    margin-bottom: 30px;
    animation: fadeInUp 0.8s ease-out 0.2s both;
  }

  .error-logo :global(.fallback-logo) {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    opacity: 0.8;
    border-radius: 100px;
  }

  .error-logo :global(img) {
    width: 80px;
    height: 80px;
    opacity: 0.8;
    border-radius: 100px;
    object-fit: cover;
  }

  /* 错误信息样式 */
  .error-header {
    margin-bottom: 40px;
    animation: fadeInUp 0.8s ease-out 0.4s both;
  }

  .error-code {
    font-size: 120px;
    font-weight: 900;
    color: #fff;
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    line-height: 1;
  }

  .error-header h1 {
    color: #fff;
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 15px 0;
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  .error-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
  }

  /* 操作按钮样式 */
  .error-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 40px;
    animation: fadeInUp 0.8s ease-out 0.6s both;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-weight: 600;
    padding: 14px 28px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
    cursor: pointer;
    font-family: inherit;
  }

  .back-link.primary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .back-link.primary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
  }

  .back-link.secondary {
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .back-link.secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
  }

  /* 社交图标样式 */
  .error-social {
    display: flex;
    justify-content: center;
    gap: 15px;
    animation: fadeInUp 0.8s ease-out 0.8s both;
  }

  .social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
  }

  .social-icon:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: #fff;
    transform: translateY(-2px) scale(1.1);
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 30px, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .error-card {
      padding: 40px 30px;
    }

    .error-code {
      font-size: 80px;
    }

    .error-header h1 {
      font-size: 24px;
    }

    .error-subtitle {
      font-size: 14px;
    }

    .error-actions {
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }

    .back-link {
      width: 200px;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .error-card {
      padding: 30px 20px;
    }

    .error-code {
      font-size: 60px;
    }

    .error-header h1 {
      font-size: 20px;
    }

    .error-logo :global(.fallback-logo) {
      width: 60px;
      height: 60px;
    }

    .error-logo :global(img) {
      width: 60px;
      height: 60px;
    }

    .back-link {
      padding: 12px 24px;
      font-size: 12px;
    }

    .social-icon {
      width: 35px;
      height: 35px;
      font-size: 14px;
    }
  }
</style>
