<script>
  import { page } from '$app/stores';
  import ParticlesBackground from '$lib/components/ParticlesBackground.svelte';
</script>

<svelte:head>
  <title>页面未找到 - {$page.status}</title>
</svelte:head>

<main class="error-page">
  <ParticlesBackground />
  <div class="background-overlay"></div>
  
  <div class="error-content">
    <div class="error-card">
      <div class="error-header">
        <h1>页面未找到</h1>
        <div class="error-code">{$page.status}</div>
      </div>
      
      <div class="error-body">
        <p>您请求的页面不存在、或已被删除、或暂时不可用。</p>
        <p>
          <a href="/" class="back-link">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
              <path fill="#00ad9f" d="M11.9998836,4.09370803 L8.55809517,7.43294953 C8.23531459,7.74611298 8.23531459,8.25388736 8.55809517,8.56693769 L12,11.9062921 L9.84187871,14 L4.24208544,8.56693751 C3.91930485,8.25388719 3.91930485,7.74611281 4.24208544,7.43294936 L9.84199531,2 L11.9998836,4.09370803 Z"></path>
            </svg>
            返回网站首页
          </a>
        </p>
      </div>
    </div>
  </div>
</main>

<style>
  .error-page {
    position: relative;
    min-height: 100vh;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }
  
  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 0;
  }
  
  .error-content {
    position: relative;
    z-index: 2;
    max-width: 500px;
    width: 100%;
  }
  
  .error-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    backdrop-filter: blur(10px);
    animation: fadeInUp 0.8s ease-out;
  }
  
  .error-header h1 {
    color: #fff;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 20px 0;
    font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
  }
  
  .error-code {
    font-size: 72px;
    font-weight: 900;
    color: #00ad9f;
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
  }
  
  .error-body p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 20px 0;
  }
  
  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #00ad9f;
    text-decoration: none;
    font-weight: 600;
    padding: 12px 24px;
    border: 2px solid #00ad9f;
    border-radius: 4px;
    transition: all 0.3s ease;
  }
  
  .back-link:hover {
    background-color: #00ad9f;
    color: #fff;
    transform: translateY(-2px);
  }
  
  .back-link svg {
    transition: transform 0.3s ease;
  }
  
  .back-link:hover svg {
    transform: translateX(-2px);
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }
  
  @media (max-width: 768px) {
    .error-card {
      padding: 30px 20px;
    }
    
    .error-header h1 {
      font-size: 24px;
    }
    
    .error-code {
      font-size: 60px;
    }
    
    .error-body p {
      font-size: 14px;
    }
  }
  
  @media (max-width: 480px) {
    .error-card {
      padding: 20px 15px;
    }
    
    .error-header h1 {
      font-size: 20px;
    }
    
    .error-code {
      font-size: 48px;
    }
    
    .back-link {
      padding: 10px 20px;
      font-size: 14px;
    }
  }
</style>
