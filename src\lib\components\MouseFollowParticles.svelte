<script lang="ts">
  import { onMount } from 'svelte';
  
  let canvas: HTMLCanvasElement;
  let ctx: CanvasRenderingContext2D;
  let particles: MouseParticle[] = [];
  let animationId: number;
  let mouse = { x: 0, y: 0 };
  
  const config = {
    particleCount: 15, // 粒子数量
    particleSize: 3, // 粒子大小
    particleLife: 60, // 粒子生命周期 (帧数)
    trailLength: 8, // 拖尾长度
    colors: ['#ffffff', '#00ad9f', '#64b5f6', '#81c784'], // 粒子颜色
    speed: 0.8, // 跟随速度
    spread: 20 // 粒子散布范围
  };
  
  class MouseParticle {
    x: number;
    y: number;
    targetX: number;
    targetY: number;
    life: number;
    maxLife: number;
    size: number;
    color: string;
    vx: number;
    vy: number;
    
    constructor(x: number, y: number) {
      this.x = x + (Math.random() - 0.5) * config.spread;
      this.y = y + (Math.random() - 0.5) * config.spread;
      this.targetX = x;
      this.targetY = y;
      this.life = config.particleLife;
      this.maxLife = config.particleLife;
      this.size = Math.random() * config.particleSize + 1;
      this.color = config.colors[Math.floor(Math.random() * config.colors.length)];
      this.vx = (Math.random() - 0.5) * 2;
      this.vy = (Math.random() - 0.5) * 2;
    }
    
    update() {
      // 粒子向目标位置移动
      const dx = this.targetX - this.x;
      const dy = this.targetY - this.y;
      
      this.x += dx * config.speed * 0.1 + this.vx;
      this.y += dy * config.speed * 0.1 + this.vy;
      
      // 减少速度
      this.vx *= 0.98;
      this.vy *= 0.98;
      
      // 减少生命值
      this.life--;
    }
    
    draw() {
      const alpha = this.life / this.maxLife;
      const size = this.size * alpha;
      
      ctx.save();
      ctx.globalAlpha = alpha * 0.8;
      ctx.fillStyle = this.color;
      ctx.shadowBlur = 10;
      ctx.shadowColor = this.color;
      
      ctx.beginPath();
      ctx.arc(this.x, this.y, size, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.restore();
    }
    
    isDead() {
      return this.life <= 0;
    }
  }
  
  function addParticle(x: number, y: number) {
    particles.push(new MouseParticle(x, y));
    
    // 限制粒子数量
    if (particles.length > config.particleCount * config.trailLength) {
      particles.splice(0, particles.length - config.particleCount * config.trailLength);
    }
  }
  
  function updateParticles() {
    // 更新粒子
    for (let i = particles.length - 1; i >= 0; i--) {
      particles[i].update();
      
      // 移除死亡的粒子
      if (particles[i].isDead()) {
        particles.splice(i, 1);
      }
    }
  }
  
  function drawParticles() {
    // 绘制粒子
    particles.forEach(particle => {
      particle.draw();
    });
  }
  
  function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    updateParticles();
    drawParticles();
    
    animationId = requestAnimationFrame(animate);
  }
  
  function handleMouseMove(event: MouseEvent) {
    const rect = canvas.getBoundingClientRect();
    mouse.x = event.clientX - rect.left;
    mouse.y = event.clientY - rect.top;
    
    // 添加新粒子
    for (let i = 0; i < 2; i++) {
      addParticle(mouse.x, mouse.y);
    }
  }
  
  function handleResize() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  }
  
  onMount(() => {
    const context = canvas.getContext('2d');
    if (!context) return;
    ctx = context;
    
    handleResize();
    animate();
    
    // 事件监听
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('resize', handleResize);
    
    return () => {
      cancelAnimationFrame(animationId);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('resize', handleResize);
    };
  });
</script>

<canvas bind:this={canvas} class="mouse-particles"></canvas>

<style>
  .mouse-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
  }
</style>
