# Svelte 重构项目说明

## 项目概述

这是一个使用 Svelte 重构的个人主页项目，原项目使用 jQuery 和传统的 HTML/CSS/JS 技术栈。重构后的项目具有更好的组件化结构、更现代的开发体验和更好的性能。

## 重构完成的功能

### 1. 组件化架构
- **PageLoader.svelte** - 页面加载动画组件
- **ParticlesBackground.svelte** - 粒子动画背景组件
- **Logo.svelte** - Logo 显示组件
- **MultiLanguageCarousel.svelte** - 多语言轮播组件
- **SocialIcons.svelte** - 社交图标组件
- **Footer.svelte** - 页脚和计时器组件

### 2. 主要特性
- ✅ 响应式设计，适配移动端和桌面端
- ✅ 粒子动画背景效果
- ✅ 多语言欢迎信息轮播
- ✅ 社交媒体链接图标
- ✅ 运行时间计时器
- ✅ 页面加载动画
- ✅ 现代化的 CSS 动画效果

### 3. 技术栈
- **Svelte 5** - 现代化的前端框架
- **SvelteKit** - 全栈 Web 应用框架
- **Vite** - 快速的构建工具
- **TypeScript** - 类型安全的 JavaScript
- **CSS3** - 现代化的样式和动画

## 安装和运行

### 1. 安装依赖
```bash
pnpm install
# 或者
npm install
```

### 2. 开发模式
```bash
pnpm dev
# 或者
npm run dev
```

### 3. 构建生产版本
```bash
pnpm build
# 或者
npm run build
```

### 4. 预览生产版本
```bash
pnpm preview
# 或者
npm run preview
```

## 文件结构

```
src/
├── lib/
│   └── components/          # 可复用组件
│       ├── PageLoader.svelte
│       ├── ParticlesBackground.svelte
│       ├── Logo.svelte
│       ├── MultiLanguageCarousel.svelte
│       ├── SocialIcons.svelte
│       └── Footer.svelte
├── routes/
│   ├── +layout.svelte      # 全局布局
│   └── +page.svelte        # 主页
├── app.css                 # 全局样式
├── app.html               # HTML 模板
└── app.d.ts               # TypeScript 声明

static/
└── images/                # 静态图片资源
    ├── logo.gif           # 网站 Logo
    ├── partical-bg.webp   # 背景图片
    └── nc-fav.ico         # 网站图标
```

## 需要添加的资源文件

请将以下文件放入 `static/images/` 文件夹：

1. **logo.gif** - 网站 Logo 图片
2. **partical-bg.webp** - 背景图片
3. **nc-fav.ico** - 网站图标

## 组件使用说明

### 1. Logo 组件
```svelte
<Logo 
  logoSrc="/images/logo.gif" 
  logoAlt="Logo" 
  href="https://blog.nmslwsnd.com" 
  target="_blank" 
/>
```

### 2. 社交图标组件
```svelte
<SocialIcons 
  socialLinks={[
    { icon: 'fa-user', href: 'https://blog.nmslwsnd.com', title: 'Blog' },
    { icon: 'fa-cloud', href: 'https://nextcloud.nmslwsnd.com', title: 'NextCloud' }
  ]} 
/>
```

### 3. 多语言轮播组件
```svelte
<MultiLanguageCarousel 
  messages={[
    { title: 'Hello', subtitle: 'Welcome to Cool\'s Personal Pages!' },
    { title: '你好', subtitle: '欢迎来到 Cool 的个人主页！' }
  ]} 
/>
```

## 自定义配置

### 1. 修改粒子动画参数
在 `ParticlesBackground.svelte` 中修改 `particleConfig` 对象：

```javascript
const particleConfig = {
  count: 80,           // 粒子数量
  color: '#ffffff',    // 粒子颜色
  opacity: 0.5,        // 粒子透明度
  size: 3,             // 粒子大小
  speed: 0.5,          // 移动速度
  lineDistance: 150,   // 连线距离
  lineOpacity: 0.4     // 连线透明度
};
```

### 2. 修改社交链接
在 `+page.svelte` 中修改 `SocialIcons` 组件的 `socialLinks` 属性。

### 3. 修改多语言信息
在 `+page.svelte` 中修改 `MultiLanguageCarousel` 组件的 `messages` 属性。

## 性能优化

1. **代码分割** - SvelteKit 自动进行代码分割
2. **Tree Shaking** - 自动移除未使用的代码
3. **CSS 优化** - 自动压缩和优化 CSS
4. **图片优化** - 建议使用 WebP 格式的图片
5. **懒加载** - 组件按需加载

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 部署

项目可以部署到任何支持静态文件的服务器：

- Vercel
- Netlify
- GitHub Pages
- 传统的 Web 服务器

## 开发建议

1. 使用 TypeScript 进行类型检查
2. 遵循 Svelte 的最佳实践
3. 保持组件的单一职责原则
4. 使用 CSS 变量进行主题定制
5. 定期更新依赖包

## 故障排除

### 1. 粒子动画不显示
- 检查浏览器是否支持 Canvas API
- 确保没有 JavaScript 错误

### 2. 图片不显示
- 确保图片文件存在于 `static/images/` 文件夹
- 检查图片路径是否正确

### 3. 样式问题
- 检查 CSS 是否正确加载
- 确保没有样式冲突

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
