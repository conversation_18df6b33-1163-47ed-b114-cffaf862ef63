/*
++++++++++++++++++++++++++++++++++++++++++++++++++++++
[ MASTER STYLESHEET ]
AUTHOR : NCode.Art
PROJECT : NC-Hold Coming-Soon Page
VERSION : 2.0
++++++++++++++++++++++++++++++++++++++++++++++++++++++
*/

/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	INDEX
++++++++++++++++++++++++++++++++++++++++++++++++++++++
	1> IMPORT FONTS
	2> RESET
	3> HELPER-CLASSES
		- GRID GUTTER SETTING
		- MARGIN
		- ALIGNMENT CLASSES
		- DISPLAY
		- BOR<PERSON>R
	4> ANIMATION
		- FADE-IN-UP
		- FADE-OUT-UP
	5> GENERAL
		- FONTS
		- TYPOGRAPHY
		- BUTTON
		- TITLE
		- LOADER
		- CAROUSEL COMMON STYLE
		- SMOOTH EFFECT
		- OVERLAY
		- NAVIGATION
	6> THEME STYLIES
		- SINGLE IMAGE
	7> HOME PAGE
		- LOGO
		- TAGLINE
		- SUBSCRIBE
		- SOCIAL-ICON
		- COPYRIGHTS
	8> ABOUT PAGE
		- MEASURE-BOX
	9> SERVICES PAGE
		- INFO-BOX
	10> TIME PAGE
		- COUNTDOWN-CLOCK
	11> CONTACT PAGE
		- CONTACT FORM
		- CONTACT-BOX
	12> OUR-TEAM PAGE
		- TEAM-BOX
	13> PORTFOLIO PAGE
		- PORTFOLIO-CAROUSEL
		- PORTFOLIO BUTTON
		- PORTFOLIO BOX
		- PORTFOLIO DETAIL
	14> PORTFOLIO-1 PAGE
		- PORT2-BOX
-------------------------------------------------------*/


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	RESET
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
*{ 
	padding: 0; 
	margin: 0; 
	vertical-align: baseline; 
}
a{ text-decoration: none !important; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	HELPER-CLASSES
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/

/*  GRID GUTTER SETTING
---------------------------------*/
.gt0    { margin-right: 0px; margin-left: 0px; }
.gt1    { margin-right: -1px; margin-left: 0px; }
.gt2    { margin-right: -1px;  margin-left: -1px; }
.gt4    { margin-right: -2px;  margin-left: -2px; }
.gt10   { margin-right: -5px;  margin-left: -5px; }
.gt12   { margin-right: -6px;  margin-left: -6px; }
.gt14   { margin-right: -7px;  margin-left: -7px; }
.gt16   { margin-right: -8px;  margin-left: -8px; }
.gt18   { margin-right: -9px;  margin-left: -9px; }
.gt20   { margin-right: -10px; margin-left: -10px; }
.gt22   { margin-right: -11px; margin-left: -11px; }
.gt24   { margin-right: -12px; margin-left: -12px; }
.gt26   { margin-right: -13px; margin-left: -13px; }
.gt28   { margin-right: -14px; margin-left: -14px; }
.gt30   { margin-right: -15px; margin-left: -15px; }
.gt40   { margin-right: -20px; margin-left: -20px; }
.gt50   { margin-right: -25px; margin-left: -25px; }
.gt60   { margin-right: -30px; margin-left: -30px; }
.gt70   { margin-right: -35px; margin-left: -35px; }
.gt80   { margin-right: -40px; margin-left: -40px; }
.gt90   { margin-right: -45px; margin-left: -45px; }
.gt100  { margin-right: -50px; margin-left: -50px; }

.gt0 > [class*="col"]   { padding-right: 0px;  padding-left: 0px; }
.gt1 > [class*="col"]   { padding-right: 1px;  padding-left: 0px; }
.gt2 > [class*="col"]   { padding-right: 1px;  padding-left: 1px; }
.gt4 > [class*="col"]   { padding-right: 2px;  padding-left: 2px; }
.gt10 > [class*="col"]  { padding-right: 5px;  padding-left: 5px; }
.gt12 > [class*="col"]  { padding-right: 6px;  padding-left: 6px; }
.gt14 > [class*="col"]  { padding-right: 7px;  padding-left: 7px; }
.gt16 > [class*="col"]  { padding-right: 8px;  padding-left: 8px; }
.gt18 > [class*="col"]  { padding-right: 9px;  padding-left: 9px; }
.gt20 > [class*="col"]  { padding-right: 10px; padding-left: 10px; }
.gt22 > [class*="col"]  { padding-right: 11px; padding-left: 11px; }
.gt24 > [class*="col"]  { padding-right: 12px; padding-left: 12px; }
.gt26 > [class*="col"]  { padding-right: 13px; padding-left: 13px; }
.gt28 > [class*="col"]  { padding-right: 14px; padding-left: 14px; }
.gt30 > [class*="col"]  { padding-right: 15px; padding-left: 15px; }
.gt40 > [class*="col"]  { padding-right: 20px; padding-left: 20px; }
.gt50 > [class*="col"]  { padding-right: 25px; padding-left: 25px; }
.gt60 > [class*="col"]  { padding-right: 30px; padding-left: 30px; }
.gt70 > [class*="col"]  { padding-right: 35px; padding-left: 35px; }
.gt80 > [class*="col"]  { padding-right: 40px; padding-left: 40px; }
.gt90 > [class*="col"]  { padding-right: 45px; padding-left: 45px; }
.gt100 > [class*="col"]     { padding-right: 50px; padding-left: 50px; }

/*  MARGIN
---------------------------------*/
/* MARGIN-TOP */
.mt0    { margin-top: 0px; }
.mt10   { margin-top: 10px; }
.mt20   { margin-top: 20px; }
.mt30   { margin-top: 30px; }
.mt40   { margin-top: 40px; }
.mt50   { margin-top: 50px; }
.mt60   { margin-top: 60px; }
.mt70   { margin-top: 70px; }
.mt80   { margin-top: 80px; }
.mt90   { margin-top: 90px; }
.mt100  { margin-top: 100px; }

/* MARGIN-RIGHT */
.mr0    { margin-right: 0px; }
.mr10   { margin-right: 10px; }
.mr20   { margin-right: 20px; }
.mr30   { margin-right: 30px; }
.mr40   { margin-right: 40px; }
.mr50   { margin-right: 50px; }
.mr60   { margin-right: 60px; }
.mr70   { margin-right: 70px; }
.mr80   { margin-right: 80px; }
.mr90   { margin-right: 90px; }
.mr100  { margin-right: 100px; }

/* MARGIN-BOTTOM */
.mb0    { margin-bottom: 0px; }
.mb10   { margin-bottom: 10px; }
.mb20   { margin-bottom: 20px; }
.mb30   { margin-bottom: 30px; }
.mb40   { margin-bottom: 40px; }
.mb50   { margin-bottom: 50px; }
.mb60   { margin-bottom: 60px; }
.mb70   { margin-bottom: 70px; }
.mb80   { margin-bottom: 80px; }
.mb90   { margin-bottom: 90px; }
.mb100  { margin-bottom: 100px; }

/* MARGIN-LEFT */
.ml0    { margin-left: 0px; }
.ml10   { margin-left: 10px; }
.ml20   { margin-left: 20px; }
.ml30   { margin-left: 30px; }
.ml40   { margin-left: 40px; }
.ml50   { margin-left: 50px; }
.ml60   { margin-left: 60px; }
.ml70   { margin-left: 70px; }
.ml80   { margin-left: 80px; }
.ml90   { margin-left: 90px; }
.ml100  { margin-left: 100px; }

/* ALIGNMENT CLASSES
---------------------------------*/

/* VERTICAL MIDDLE */
.vm{ display: table; }
.vm .vm-item{ display: table-cell; vertical-align: middle; }

/* VERTICAL & HORIZONTAL MIDDLE */
.vhm { position: relative; }
.vhm > .vhm-item {
	-ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    position: absolute;
    top: 50%;
    left: 50%;
}

/* TEXT ALIGN */
.align-center{ text-align: center !important; }
.align-right{ text-align: right !important; }
.align-left{ text-align: left !important; }

/* DISPLAY
---------------------------------*/
.display_none{ display: none; }
.display_block{ display: block; }

/* BORDER
---------------------------------*/
.bd-t{ border-top: 1px solid; }
.bd-r{ border-right: 1px solid; }
.bd-b{ border-bottom: 1px solid; }
.bd-l{ border-left: 1px solid; }

.bd-t, 
.bd-r, 
.bd-b, 
.bd-l{
	border-color: rgba(255,255,255,0.2);
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	ANIMATION
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.anim {
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}
#home-page.active-home{ opacity: 1;}
#home-page{ opacity: 0; }

/*	FADE-IN-UP
------------------------------*/
@-webkit-keyframes fadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 20px, 0);
		transform: translate3d(0, 20px, 0);
	}

	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@keyframes fadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 20px, 0);
		transform: translate3d(0, 20px, 0);
	}

	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}

/*	FADE-OUT-UP
------------------------------*/
@-webkit-keyframes fadeOutUp {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
		-webkit-transform: translate3d(0, -20px, 0);
		transform: translate3d(0, -20px, 0);
	}
}
@keyframes fadeOutUp {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
		-webkit-transform: translate3d(0, -20px, 0);
		transform: translate3d(0, -20px, 0);
	}
}

.s01 { -webkit-animation-duration: 01s; animation-duration: 01s; }
.s02 { -webkit-animation-duration: 02s; animation-duration: 02s; }
.s04 { -webkit-animation-duration: 04s; animation-duration: 04s; }
.s010 { -webkit-animation-duration: 010s; animation-duration: 010s; }

.delay-0s { -webkit-animation-delay: 0s; animation-delay: 0s; }
.delay-0-1s { -webkit-animation-delay: 0.1s; animation-delay: 0.1s; }
.delay-0-2s { -webkit-animation-delay: 0.2s; animation-delay: 0.2s; }
.delay-0-3s { -webkit-animation-delay: 0.3s; animation-delay: 0.3s; }
.delay-0-4s { -webkit-animation-delay: 0.4s; animation-delay: 0.4s; }
.delay-0-5s { -webkit-animation-delay: 0.5s; animation-delay: 0.5s; }
.delay-0-6s { -webkit-animation-delay: 0.6s; animation-delay: 0.6s; }
.delay-0-7s { -webkit-animation-delay: 0.7s; animation-delay: 0.7s; }
.delay-0-8s { -webkit-animation-delay: 0.8s; animation-delay: 0.8s; }
.delay-0-9s { -webkit-animation-delay: 0.9s; animation-delay: 0.9s; }

.delay-1s { -webkit-animation-delay: 1s; animation-delay: 1s; }
.delay-1-1s { -webkit-animation-delay: 1.1s; animation-delay: 1.1s; }
.delay-1-2s { -webkit-animation-delay: 1.2s; animation-delay: 1.2s; }
.delay-1-3s { -webkit-animation-delay: 1.3s; animation-delay: 1.3s; }
.delay-1-4s { -webkit-animation-delay: 1.4s; animation-delay: 1.4s; }
.delay-1-5s { -webkit-animation-delay: 1.5s; animation-delay: 1.5s; }
.delay-1-6s { -webkit-animation-delay: 1.6s; animation-delay: 1.6s; }
.delay-1-7s { -webkit-animation-delay: 1.7s; animation-delay: 1.7s; }
.delay-1-8s { -webkit-animation-delay: 1.8s; animation-delay: 1.8s; }
.delay-1-9s { -webkit-animation-delay: 1.9s; animation-delay: 1.9s; }
.delay-2s { -webkit-animation-delay: 2s; animation-delay: 2s; }
.delay-2-1s { -webkit-animation-delay: 2.1s; animation-delay: 2.1s; }
.delay-3s { -webkit-animation-delay: 3s; animation-delay: 3s; }
.delay-3-5s { -webkit-animation-delay: 3.5s; animation-delay: 3.5s; }
.delay-4s { -webkit-animation-delay: 4s; animation-delay: 4s; }
.delay-4-5s { -webkit-animation-delay: 4.5s; animation-delay: 4.5s; }
.delay-5s { -webkit-animation-delay: 5s; animation-delay: 5s; }
.delay-5-5s { -webkit-animation-delay: 5.5s; animation-delay: 5.5s; }
.delay-6s { -webkit-animation-delay: 6s; animation-delay: 6s; }
.delay-6-5s { -webkit-animation-delay: 6.5s; animation-delay: 6.5s; }
.delay-7s { -webkit-animation-delay: 7s; animation-delay: 7s; }
.delay-7-5s { -webkit-animation-delay: 7.5s; animation-delay: 7.5s; }
.delay-8s { -webkit-animation-delay: 8s; animation-delay: 8s; }
.delay-8-5s { -webkit-animation-delay: 8.5s; animation-delay: 8.5s; }
.delay-9s { -webkit-animation-delay: 9s; animation-delay: 9s; }
.delay-9-5s { -webkit-animation-delay: 9.5s; animation-delay: 9.5s; }
.delay-10s { -webkit-animation-delay: 10s; animation-delay: 10s; }
.delay-10-5s { -webkit-animation-delay: 10.5s; animation-delay: 10.5s; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	GENERAL
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
body { background-color: #fff; color: #fff; }
img{ width: 100%; max-width: 100%; }
#main{ box-shadow: none; }
.main-wrapper{ position: relative; }
.container{ width: 1170px; }
html.ie .nc-content-section{ overflow: hidden; }

/*	FONTS
------------------------------*/
.ff-1{ font-family: 'Roboto', sans-serif; }
.ff-2{ font-family: 'Open Sans', sans-serif; }

.link-box .text,
.tagline h1,
.tagline p,
.subscribe .form-control,
.dash .digit,
.dash_title,
.contact-box .text p,
.contact-box .text a,
.btn.btn-color,
#notifyMe .message,
.title-wrapper p,
.measure-box .per .no,
.project-detail .h-text,
.package-box .name,
.package-box .amount-wrp .amount,
.about-us-page.s1 .large-text{
	font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
}
.info-box .text p,
.team-box .text .small-text,
.project-detail p{
	font-family: 'Open Sans', sans-serif;	
}

/*	TYPOGRAPHY
------------------------------*/
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6{
	font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
	margin: 0;
}

/*	BUTTON
------------------------------*/
.btn{
	padding: 12px 20px;
    margin-bottom: 0px;
    font-size: 14px;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 2px;
    border-radius: 0;
}
.btn.btn-color{
	background-color: #fff;
	border: 2px solid;
	border-color: transparent;
	color: #000;
}
.btn.btn-color:hover{
	background-color: transparent;
	border-color: #fff;
	color: #fff;
}

/*	TITLE
------------------------------*/
.title-wrapper{ margin-bottom: 60px; }
.title-wrapper .title{ 
	text-align: center; 
	text-transform: uppercase;
	font-weight: 900;
	letter-spacing: 2px;
	color: #fff;
}
.title-wrapper .line{ 
	width: 100px; 
	height: 2px;
	background-color: rgba(255,255,255,0.2);
	margin: auto;
	display: block;
	margin-top: 20px;
}
.title-wrapper p{
	font-size: 22px;
	font-weight: 100;
	line-height: 1.3;
	text-align: center;
	margin: auto;
	margin-top: 20px;
	width: 60%;
}

.title-wrapper.sub{ margin-bottom: 15px; }
.title-wrapper.sub .title{ 
	text-align: left; 
	color: #fff; 
	font-weight: 500;
	letter-spacing: 1px;
}
.title-wrapper.sub .line{ margin-left: 0; margin-top: 10px; }

/*	LOADER
------------------------------*/
.page-loader-wrapper{
	position: fixed;
	width: 100%;
	height: 100%;
	background-color: #000;
	z-index: 99;
}
.pg-loader.page-loader-wrapper{ background-color: rgba(0,0,0,0.8); }
.loader {
	width: 40px;
	height: 40px;
	margin: auto;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 999;
}
.loader:before {
	content: '';
	width: 40px;
	height: 5px;
	background: #fff;
	opacity: 0.2;
	position: absolute;
	top: 49px;
	left: 0;
	border-radius: 50%;
	-webkit-animation: shadow .5s linear infinite;
	-moz-animation: shadow .5s linear infinite;
	-ms-animation: shadow .5s linear infinite;
	animation: shadow .5s linear infinite;
}
.loader:after {
	content: '';
	width: 40px;
	height: 40px;
	background: #fff;
	-webkit-animation: animate .5s linear infinite;
	-moz-animation: animate .5s linear infinite;
	-ms-animation: animate .5s linear infinite;
	animation: animate .5s linear infinite;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 3px;
}
@-webkit-keyframes animate {
	17% {
		border-bottom-right-radius: 3px;
	}
	25% {
		-webkit-transform: translateY(9px) rotate(22.5deg);
	}
	50% {
		-webkit-transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}
	75% {
		-webkit-transform: translateY(9px) rotate(67.5deg);
	}
	100% {
		-webkit-transform: translateY(0) rotate(90deg);
	}
}
@-moz-keyframes animate {
	17% {
		border-bottom-right-radius: 3px;
	}
	25% {
		-moz-transform: translateY(9px) rotate(22.5deg);
	}
	50% {
		-moz-transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}
	75% {
		-moz-transform: translateY(9px) rotate(67.5deg);
	}
	100% {
		-moz-transform: translateY(0) rotate(90deg);
	}
}
@-ms-keyframes animate {
	17% {
		border-bottom-right-radius: 3px;
	}
	25% {
		-ms-transform: translateY(9px) rotate(22.5deg);
	}
	50% {
		-ms-transform: translateY(18px) scale(1, 0.9) rotate(45deg);
		border-bottom-right-radius: 40px;
	}
	75% {
		-ms-transform: translateY(9px) rotate(67.5deg);
	}
	100% {
		-ms-transform: translateY(0) rotate(90deg);
	}
}
@-webkit-keyframes shadow {
	0%,
	100% {
		-webkit-transform: scale(1, 1);
	}
	50% {
		-webkit-transform: scale(1.2, 1);
	}
}
@-moz-keyframes shadow {
	0%,
	100% {
		-moz-transform: scale(1, 1);
	}
	50% {
		-moz-transform: scale(1.2, 1);
	}
}
@-ms-keyframes shadow {
	0%,
	100% {
		-ms-transform: scale(1, 1);
	}
	50% {
		-ms-transform: scale(1.2, 1);
	}
}
@keyframes shadow {
	0%,
	100% {
		transform: scale(1, 1);
	}
	50% {
		transform: scale(1.2, 1);
	}
}

/*	CAROUSEL COMMON STYLE
---------------------------------*/
.carousel { margin-left: -15px; margin-right: -15px; }
.carousel .item {
	padding-left: 15px;
	padding-right: 15px;
	margin: 10px 0px;
}

/* CAROUSEL BUTTON */
.carousel-btn{
	width: 100%;
	height: auto;
	margin-top: 20px;
	text-align: center;
}
.carousel-btn .prev{ border-right: 1px solid; left: 2.5px;}
.carousel-btn .next{ border-left: 1px solid; right: 2.5px; }
.carousel-btn .btn {
	color: #e8e9ea;
	display: inline-block;
	width: 50px;
	height: 50px;
	border-color: rgba(255,255,255,0.15);
	cursor: pointer;
}
.carousel-btn .btn i { font-size: 30px; text-align: center; }
@-webkit-keyframes shake {
	0% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
	50% {
		-webkit-transform: translate3d(5px, 0, 0);
		transform: translate3d(5px, 0, 0);
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
@-moz-keyframes shake {
	0% {
		-moz-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
	50% {
		-moz-transform: translate3d(5px, 0, 0);
		transform: translate3d(5px, 0, 0);
	}
	100% {
		-moz-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
@-ms-keyframes shake {
	0% {
		-ms-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
	50% {
		-ms-transform: translate3d(5px, 0, 0);
		transform: translate3d(5px, 0, 0);
	}
	100% {
		-ms-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
@keyframes shake {
	0% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
	50% {
		-webkit-transform: translate3d(5px, 0, 0);
		transform: translate3d(5px, 0, 0);
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
.carousel-btn .btn:hover i{ 
	-webkit-animation-name: shake;
	-moz-animation-name: shake;
	-ms-animation-name: shake;
	animation-name: shake;

	-webkit-animation-duration: 1s;
	-moz-animation-duration: 1s;
	-ms-animation-duration: 1s;
	animation-duration: 1s;

	-webkit-animation-fill-mode: both;
	-moz-animation-fill-mode: both;
	-ms-animation-fill-mode: both;
	animation-fill-mode: both;

	-webkit-animation-iteration-count: infinite;
	-moz-animation-iteration-count: infinite;
	-ms-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
}

/* CAROUSEL PAGINATION */
.owl-theme .owl-controls .owl-page span{ background: transparent; border: 1px solid #cecece; }
.owl-theme .owl-controls.clickable .owl-page:hover span{ background-color: #fff; border: 1px solid #fff; }
.owl-theme .owl-controls .owl-page.active span{ background-color: #fff; border: 1px solid #fff; }

/*	SMOOTH EFFECT
------------------------------*/
.no-touch .nc-menu-close,
.subscribe .form-control,
.subscribe .icon,
.social-icon .icon .box,
.contact-box .icon,
.contact-form .form-control,
.contact-box,
.info-box .icon,
.info-box .icon .box,
.team-box .social-link .icon,
.team-box .member-detail,
.btn.btn-color,
.port2-box .port-detail,
.package-box,
.team-box-1 .social-link .icon{
	-webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -o-transition: all 0.3s linear;
    -ms-transition: all 0.3s linear;
    transition: all 0.3s linear;
}

/*	OVERLAY
------------------------------*/
.nc-overlay{ 
	background-color: rgba(255,255,255,0.1); 
	opacity: 0;
	position: absolute;
}
.overlay{ 
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

/*	NAVIGATION
------------------------------*/
.nc-menu-trigger{ top: 30px; right: 30px; }
.nc-menu-trigger span{ background-color: #fff; }
.nc-menu-container{ width: 300px; }
.nc-menu-container.visible-on{ background-color: #fff;  }
.nc-menu-container .nc-menu{ background-color: #fff; }
.nc-menu-container .nav-header{ 
	width: 100%; 
	height: 80px;
	border-bottom: 1px solid rgba(0,0,0,0.1);
}
.nc-menu-container .nav-header .h-text{ color: #fff; }

/* CLOSE-BUTTON */
.nc-menu-close{ right: 50%; margin-right: -22px; }
.nc-menu-close::after, 
.nc-menu-close::before{
	background-color: #000;
}
.no-touch .nc-menu-close:hover{ 
	background-color: #000; 
	opacity: 1;
}
.no-touch .nc-menu-close:hover::after,
.no-touch .nc-menu-close:hover::before{ 
	background-color: #fff; 
}
.nc-menu li{ 
	float: none;
	border: none; 
	width: 100%; 
	height: 142px;
	min-height: 142px;
}

/* LINK-BOX */
.nc-menu li .link-box{ border-bottom: 1px solid rgba(0,0,0,0.1);}
.nc-menu li:last-child .link-box{ padding-bottom: 0; border-bottom: none; }
.link-box{ color: #000; }
.link-box span{ display: block; }
.link-box .icon{ 
	width: 52px;
	height: 52px;
	margin: auto;
	margin-bottom: 10px; 
}
.link-box .icon i{ font-size: 36px; }
.link-box .text{ 
	text-transform: uppercase;
	font-size: 16px; 
	font-weight: 900;
	letter-spacing: 2px;
	line-height: 1; 
}
.nc-menu li .link-box:focus{
	outline: none;
	color: #fff;
}
.nc-menu li .link-box:hover{ background-color: #000; color: #fff; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	THEME STYLIES
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/

/*	SINGLE IMAGE
------------------------------*/
.single-image .background{
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	HOME PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/

/*	LOGO
------------------------------*/
.logo{ text-align: center; margin-bottom: 60px; }
.logo .logo-wrp{ display: inline-block; width: 120px; }
.logo .logo-wrp img{ width: 100%; max-width: 100%;border-radius:100px; }

/*	TAGLINE
------------------------------*/
.text-carousel .carousel .item{
	margin: 0;
}
.tagline{ text-align: center; margin-bottom: 40px; }
.tagline h1{
	color: #fff;
	font-size: 60px;
	font-weight: 900;
	margin-bottom: 30px;
}
.tagline p{
	font-size: 24px;
	line-height: 1.5;
	color: #fff;
	font-weight: 100;
	width: 70%;
	margin: auto;
}

/*	SUBSCRIBE
------------------------------*/
.subscribe{ width: 500px; margin: auto; }
.subscribe .form-control{
	border: none;
	border-radius: 0;
	background-color: transparent;
	box-shadow: none;
	border-bottom: 2px solid;
	border-color: rgba(255,255,255,0.3);
	background-color: rgba(255,255,255,0.05);
	width: 100%;
	color: #fff;
	font-size: 20px;
	height: auto;
	padding: 10px 15px;
	font-weight: 100;
	position: relative;
}
.subscribe .form-control:focus{ border-color: #fff; }
.subscribe .icon{ 
	width: 50px; 
	height: 50px; 
	position: absolute;
	right: 7px;
	top: -2px;
	color: rgba(255,255,255,0.2);
	background-color: transparent;
	border: none;
}
.subscribe .icon:focus{ outline: none; }
.subscribe .icon:hover{ color: #fff; }
.subscribe .icon i{ font-size: 30px; }
#notifyMe{ position: relative; }
#notifyMe .message{ 
	text-align: center;
    font-size: 14px;
    padding: 10px;
    margin-top: 10px;
    font-weight: 100;
}
#notifyMe i.error-text,
#notifyMe i.fa-exclamation-circle,
#notifyMe i.fa-check-circle,
#notifyMe i.fa-spin{
	position: absolute;
    right: 60px;
    top: 16px;
    font-size: 22px;
}
.subscribe .form-control::-webkit-input-placeholder { font-size: 20px; color: #fff; }
.subscribe .form-control:-moz-placeholder { font-size: 20px; color: #fff; opacity: 1; }
.subscribe .form-control::-moz-placeholder { font-size: 20px; color: #fff; opacity: 1; }
.subscribe .form-control:-ms-input-placeholder { font-size: 20px; color: #fff; }

/*	SOCIAL-ICON
------------------------------*/
.social-icon{ text-align: center; margin-top: 20px; }
.social-icon .icon{
	display: inline-block;
	width: 40px;
	height: 40px;
	color: #fff;
	margin: 0 5px;
}
.social-icon .icon .box{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
    border-bottom: 1px solid transparent;
}
.social-icon .icon i{ font-size: 20px; }
.social-icon .icon:hover{ color: #000; }
.social-icon .icon:hover .box{ border-color: #fff; background-color: #fff; }

.social-icon-big{ text-align: center; margin-top: 20px; }
.social-icon-big .icon{
	display: inline-block;
	width: 80px;
	height: 80px;
	color: #fff;
	margin: 0 5px;
}
.social-icon-big .icon .box{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
    border-bottom: 1px solid transparent;
}
.social-icon-big .icon i{ font-size: 40px; }
.social-icon-big .icon:hover{ color: #000; }
.social-icon-big .icon:hover .box{ border-color: #fff; background-color: #fff; }

/*	COPYRIGHTS
------------------------------*/
.copyrights{
	font-size: 12px;
	color: rgba(255,255,255,0.6);
	text-align: center;
	width: 100%;
	letter-spacing: 2px;
	margin-top: 30px;
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	ABOUT PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.text-info{ margin-bottom: 30px; }
.text-info p{
	font-size: 16px;
    line-height: 1.5;
    color: rgba(255,255,255,0.6);
    letter-spacing: 1px;
}

/*	MEASURE-BOX
------------------------------*/
.measure-box{
	background-color: rgba(255,255,255,0.05);
    border-bottom: 2px solid rgba(255,255,255,0.2);
    padding: 10px;
    position: relative;
    margin-bottom: 10px;
    width: 80%;
    margin-left: 0;
}
.measure-box .title{ font-weight: 100; text-transform: uppercase; }
.measure-box .per{
	position: absolute;
    display: block;
    height: 5px;
    background-color: rgba(255,255,255,0.85);
    width: 500px;
    bottom: -3px;
    left: 0;
    letter-spacing: 2px;
}
.measure-box .per .no{
	position: absolute;
	right: 0;
	bottom: 10px;
	letter-spacing: 1px;
}
.measure-box.one .per{ width: 90%; }
.measure-box.two .per{ width: 80%; }
.measure-box.three .per{ width: 90%; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	ABOUT PAGE STYLE-1
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.large-text{
	font-size: 24px;
	color: #fff;
	font-weight: 100;
	line-height: 1.5;
}
.info-box-1-wrp{
	border-left: 1px solid;
	border-color: rgba(255,255,255,0.2);
}
.info-box-1-wrp > .mb{ margin-bottom: 40px; }

/*	INFO-BOX-1
------------------------------*/
.info-box-1 .icon{
	width: 60px;
	height: 60px;
	display: block;
    position: relative;
    float: left;
}
.info-box-1 .icon i{ font-size: 24px; }
.info-box-1 .icon .box{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
    border-bottom: 2px solid;
    border-color: rgba(255,255,255,0.3);
    background-color: rgba(255,255,255,0.05);
}
.info-box-1 .text{ 
	text-align: left; 
	margin-left: 80px;
}
.info-box-1 .text .h-text{
	margin-bottom: 12px;
	text-transform: uppercase;
	display: inline-block;
}
.info-box-1 .text p{
	font-size: 16px;
    line-height: 1.5;
    color: rgba(255,255,255,0.6);
    letter-spacing: 1px;
}



/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	SERVICES PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.info-box-carousel .owl-carousel .owl-stage-outer{ padding: 10px 0; }

/*	INFO-BOX
------------------------------*/
.info-box{ cursor: pointer; }
.info-box .icon{
	width: 80px;
	height: 80px;
	margin: auto;
	display: block;
	margin-bottom: 20px;
    position: relative;
}
.info-box .icon i{ font-size: 30px; }
.info-box .icon .box{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
    border-bottom: 2px solid;
    border-color: rgba(255,255,255,0.3);
    background-color: rgba(255,255,255,0.05);
}
.info-box .text{ text-align: center; }
.info-box .text .h-text{
	margin-bottom: 20px;
	text-transform: uppercase;
	display: inline-block;
}
.info-box .text p{
	font-size: 16px;
    line-height: 1.5;
    color: rgba(255,255,255,0.6);
    padding-bottom: 30px;
    letter-spacing: 1px;
}
.info-box:hover .icon{ color: #000; }
.info-box:hover .icon .box{ background-color: #fff; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	TIME PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/

/* COUNTDOWN-CLOCK
--------------------------------*/
#countdown_dashboard{ 
	min-height: 110px; 
	text-align: center; 
	width: 100%;
}
#countdown_dashboard .inner-dashboard{ display: inline-block; }
.dash {
    float: left;
    position: relative;
    text-align: center;
    min-width: 200px;
    min-height: 100px;
    border-right: 1px solid transparent;
    padding: 20px 33px 34px 33px;
    margin: 2px;
    background-color: rgba(255,255,255,0.05);
    border-bottom: 2px solid rgba(255,255,255,0.2);
}
.dash .inner-dash{ display: inline-block; }
.dash .digit {
    font-size: 80px;
    float: left;
    text-align: center;
    color: #fff;
    position: relative;
    min-width: 35px;
    line-height: 1;
    overflow: hidden;
    font-weight: 300;
}
.dash_title {
    position: absolute;
    width: 100%;
    display: block;
    bottom: 13px;
    left: 0;
    font-size: 14px;
    color: rgba(255,255,255,0.7);
    text-transform: uppercase;
    letter-spacing: 1;
    font-weight: 100;
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	CONTACT PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
#contact .title-wrapper{ margin-bottom: 30px; }

/*  CONTACT FORM
---------------------------------*/
.contact-form .form-control{
    border: none;
    border-bottom: 2px solid;
    border-radius: 0px;
    border-color: rgba(255,255,255,0.2);  
    min-height: 46px;
    background: transparent;
    color: #fff;
    box-shadow: none;
    font-size: 16px;
    background-color: rgba(255,255,255,0.05);
}
.contact-form .input-area{ margin-bottom: 30px; position: relative; }
.contact-form textarea.form-control{ min-height: 126px; }
.contact-form .button-wrp{ text-align: center; }
.contact-form .form-control:focus{ border-color: #fff; }
.contact-form .error-form{
    position: absolute;
    bottom: 12px;
    right: 20px;
    color: #df1d37;
}
.disable.cnt-rmsg{
    position: absolute;
    bottom: -10px;
    left: 0px;
    z-index: 9999;
    width: 100%;
    text-align: center;
}
.disable.cnt-rmsg #returnmessage{ 
    color: rgba(182, 252, 177, 0.99); 
    padding: 10px 15px;
    background: rgba(12,175,0,0.5);
    margin-bottom: 0;
    opacity: 0;
    width: 50%;
    display: inline-block;
}
.form-control::-webkit-input-placeholder { font-size: 16px; color: #fff; }
.form-control:-moz-placeholder { font-size: 16px; color: #fff; opacity: 1; }
.form-control::-moz-placeholder { font-size: 16px; color: #fff; opacity: 1; }
.form-control:-ms-input-placeholder { font-size: 16px; color: #fff; }

/*  CONTACT-BOX
---------------------------------*/
.contact-box{ 
	margin-bottom: 40px; 
	border-bottom: 2px solid;
    border-color: rgba(255,255,255,0.2);
    background-color: rgba(255,255,255,0.05);
    cursor: pointer;
}
.contact-box .icon{
	width: 50px;
	height: 50px;
	float: left;
	position: relative;
}
.contact-box .icon .box{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
}
.contact-box .icon i{ font-size: 20px; }
.contact-box .text{ 
	min-height: 50px;
	margin-left: 50px; 
	padding: 0 30px;
	width: 85%;
}
html.ff .contact-box .text{ height: 50px; }
.contact-box .text p,
.contact-box .text a{
	color: #fff;
	font-weight: 100;
	font-size: 18px;
	letter-spacing: 1px;
}
.contact-box:hover{ border-color: #fff; }
.contact-box:hover .icon{ background-color: #fff; color: #000; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	OUR-TEAM PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.team-box-carousel .carousel .item{
	padding-left: 1px;
	padding-right: 1px;
}
/*  TEAM-BOX
---------------------------------*/
.team-box{
	position: relative;
	overflow: hidden;
}
.team-box .text{
	position: absolute;
	top: 0px;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255,255,255,0.9);
	text-align: center;
	opacity: 0;
}
.team-box .text,
.team-box .social-link .icon,
.team-box .text .h-text,
.team-box .small-text,
.team-box .social-link .icon,
.team-box:hover .text,
.team-box:hover .text .h-text,
.team-box:hover .small-text,
.team-box .social-link{
	-webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -o-transition: all 0.3s linear;
    -ms-transition: all 0.3s linear;
    transition: all 0.3s linear;
}
.team-box .text .h-text,
.team-box .small-text,
.team-box .social-link{
	position: relative;
	bottom: -15px;
	opacity: 0;
}

.team-box .text .h-text{
	text-transform: uppercase;
	border-bottom: 1px solid;
	border-color: rgba(0,0,0,0.5);
	padding-bottom: 5px;
	margin-bottom: 10px;
	display: inline-block;
	color: #000;
}
.team-box .small-text{
	font-size: 12px;
	font-style: italic;
	color: rgba(0,0,0,0.6);
	display: block;
	font-weight: 600;
	letter-spacing: 1px;
}
.team-box .social-link{ margin-top: 20px; }
.team-box .social-link .icon{
	width: 30px;
	height: 30px;
	color: #000;
	display: inline-block;
}
.team-box .social-link .icon i{ font-size: 16px; }

.team-box:hover .text{ opacity: 1; }
.team-box:hover .text .h-text{
	position: relative;
	bottom: 0px;
	opacity: 1;
    -webkit-transition-delay: 0.3s;
    -moz-transition-delay: 0.3s;
    -ms-transition-delay: 0.3s;
    transition-delay: 0.3s;
}
.team-box:hover .small-text{
	position: relative;
	bottom: 0px;
	opacity: 1;
    -webkit-transition-delay: 0.4s;
    -moz-transition-delay: 0.4s;
    -ms-transition-delay: 0.4s;
    transition-delay: 0.4s;
}
.team-box:hover .social-link{
	position: relative;
	bottom: 0px;
	opacity: 1;
    -webkit-transition-delay: 0.5s;
    -moz-transition-delay: 0.5s;
    -ms-transition-delay: 0.5s;
    transition-delay: 0.5s;
}
.team-box .social-link .icon:hover{
	background-color: #000;
	color: #fff;
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	 OUR-TEAM PAGE STYLE-1
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.team-box-1-carousel .carousel .item{
	padding-left: 1px;
	padding-right: 1px;
}

/*  TEAM-BOX-1
---------------------------------*/
.team-box-1{
	text-align: center;
	padding: 30px;
	background-color: rgba(255,255,255,0.1);
}
.team-box-1 .image{
	width: 120px;
	height: 120px;
	border-radius: 50%;
	margin: auto;
	margin-bottom: 20px;
	overflow: hidden;
}
.team-box-1 .image img{
	width: 100%;
	max-width: 100%;
	border-radius: 50%;
}
.team-box-1 .text .name{
	border-bottom: 2px solid;
	border-color: rgba(255,255,255,0.2);
	padding-bottom: 10px;
	margin-bottom: 15px;
	display: inline-block;
} 
.team-box-1 .text .h-text{ text-transform: uppercase; }
.team-box-1 .text .small-text{
	font-size: 12px;
	font-style: italic;
	color: rgba(255,255,255,0.6);
	display: block;
	font-weight: 600;
	letter-spacing: 1px;
	margin-top: 5px;
}
.team-box-1 .text p{
	font-size: 16px;
    line-height: 1.5;
    color: rgba(255,255,255,0.6);
    letter-spacing: 1px;
}
.team-box-1 .social-link{ margin-top: 20px; }
.team-box-1 .social-link .icon{
	width: 30px;
	height: 30px;
	color: #fff;
	display: inline-block;
}
.team-box-1 .social-link .icon i{ font-size: 16px; }
.team-box-1:hover .social-link{
	position: relative;
	bottom: 0px;
	opacity: 1;
    -webkit-transition-delay: 0.5s;
    -moz-transition-delay: 0.5s;
    -ms-transition-delay: 0.5s;
    transition-delay: 0.5s;
}
.team-box-1 .social-link .icon:hover{
	background-color: #fff;
	color: #000;
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	PORTFOLIO PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
#ajax-page.port-full{ width: 100%; height: 100%; }

/*  PORTFOLIO-CAROUSEL
---------------------------------*/
.portfolio-carousel{ overflow: hidden; }
.portfolio-carousel,
.portfolio-carousel .carousel,
.portfolio-carousel .owl-carousel
{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	margin: 0;
}
.portfolio-carousel .owl-carousel .owl-stage-outer{ 
	height: auto !important;
	-webkit-transform: translate(0px, -50%);
	-moz-transform: translate(0px, -50%);
	-ms-transform: translate(0px, -50%);
	transform: translate(0px, -50%); 
	top: 50%;
}
.portfolio-carousel .owl-carousel .owl-stage{ 
	padding: 10px 0; 
	margin: 50% 0;
}
.portfolio-carousel .carousel .item{
	padding-left: 00px;
	padding-right: 00px;
	background-color: #000;
}

/*  PORTFOLIO BUTTON
---------------------------------*/
.portfolio-carousel .carousel-btn .btn{
	position: absolute;
	top: 50%;
	margin-top: -50px;
	height: 100px;
	width: 80px;
	background-color: rgba(0,0,0,0.5);
	z-index: 9;
}
.portfolio-carousel .carousel-btn .btn i{ font-size: 60px; }
.portfolio-carousel .carousel-btn .prev{ left: 0; }
.portfolio-carousel .carousel-btn .next{ right: 0; }

/*  PORTFOLIO BOX
---------------------------------*/
.ie9 .portfolio-carousel .owl-carousel .owl-item .portfolio-box{ opacity: 1; }
.portfolio-carousel .owl-carousel .owl-item .portfolio-box{
	-webkit-animation: move-origin1 0.8s;
	-moz-animation: move-origin1 0.8s;
	-ms-animation: move-origin1 0.8s;
	animation: move-origin1 0.8s;

	-webkit-animation-fill-mode: forwards;
	-moz-animation-fill-mode: forwards;
	-ms-animation-fill-mode: forwards;
	animation-fill-mode: forwards;
}
.portfolio-carousel .owl-carousel .owl-item.active.center .portfolio-box{
	position: relative;
	z-index: 9;
	-webkit-animation: move-origin 0.8s;
	-moz-animation: move-origin 0.8s;
	-ms-animation: move-origin 0.8s;
	animation: move-origin 0.8s;

	-webkit-animation-fill-mode: forwards;
	-moz-animation-fill-mode: forwards;
	-ms-animation-fill-mode: forwards;
	animation-fill-mode: forwards;
	box-shadow: 0 0 50px 6px rgba(0,0,0,0.6);
}
.portfolio-box{ position: relative; opacity: 0.2; }

@-webkit-keyframes move-origin {
	0% {
		opacity: 0.2;
		-webkit-transform: scale(1);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1.5);
	}
}
@-moz-keyframes move-origin {
	0% {
		opacity: 0.2;
		-moz-transform: scale(1);
	}
	100% {
		opacity: 1;
		-moz-transform: scale(1.5);
	}
}
@-ms-keyframes move-origin {
	0% {
		opacity: 0.2;
		-ms-transform: scale(1);
	}
	100% {
		opacity: 1;
		-ms-transform: scale(1.5);
	}
}
@keyframes move-origin {
  0% {
  	opacity: 0.2;
    transform: scale(1);
  }
  100% {
  	opacity: 1;
    transform: scale(1.5);
  }
}

@-webkit-keyframes move-origin1 {
	0% {
		opacity: 1;
		-webkit-transform: scale(1.5);
	}
	100% {
		opacity: 0.2;
		-webkit-transform: scale(1);
	}
}
@-moz-keyframes move-origin1 {
	0% {
		opacity: 1;
		-moz-transform: scale(1.5);
	}
	100% {
		opacity: 0.2;
		-moz-transform: scale(1);
	}
}
@-ms-keyframes move-origin1 {
	0% {
		opacity: 1;
		-ms-transform: scale(1.5);
	}
	100% {
		opacity: 0.2;
		-ms-transform: scale(1);
	}
}
@keyframes move-origin1 {
	0% {
		opacity: 1;
		transform: scale(1.5);
	}

	100% {
		opacity: 0.2;
		transform: scale(1);
	}
}

/*  PORTFOLIO DETAIL
---------------------------------*/
.portfolio-detail{
	padding: 30px;
	position: absolute;
	bottom: 0;
	left: 0;
}
.portfolio-detail .h-text,
.portfolio-detail p{
	background-color: rgba(0,0,0,0.6);
	border-bottom: 1px solid rgba(255,255,255,0.2);
	display: inline-block;
	padding: 8px 15px;
	opacity: 0;
}
.portfolio-detail .h-text{
	text-transform: uppercase;
	margin-bottom: 5px;
	font-weight: 500;
	font-size: 20px;
}
.portfolio-detail p{
	display: block;
	font-size: 12px;
	font-weight: 300;
}

.portfolio-carousel .owl-item.active.center .portfolio-detail .h-text,
.portfolio-carousel .owl-item.active.center .portfolio-detail p{
	-webkit-animation-name: fadeInRight;
	animation-name: fadeInRight;
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}
.portfolio-carousel .owl-item.active.center .portfolio-detail .h-text{
	-webkit-animation-delay: 0.5s;
    -moz-animation-delay: 0.5s;
    -ms-animation-delay: 0.5s;
    animation-delay: 0.5s;
}
.portfolio-carousel .owl-item.active.center .portfolio-detail p{
	-webkit-animation-delay: 0.7s;
    -moz-animation-delay: 0.7s;
    -ms-animation-delay: 0.7s;
    animation-delay: 0.7s;
}

@-webkit-keyframes fadeInRight {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(30px, 0, 0);
		transform: translate3d(30px, 0, 0);
	}

	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}

@keyframes fadeInRight {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(30px, 0, 0);
		transform: translate3d(30px, 0, 0);
	}

	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	VIDEO-PORTFOLIO PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.video-portfolio .owl-carousel .owl-video-wrapper{ height: 300px; }
.video-portfolio .owl-carousel .owl-video-tn{ background-size: cover; background-position: center center; }

.video-portfolio .owl-carousel .owl-item .portfolio-box{
	opacity: 0.2;
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
	-webkit-transition: all 0.6s linear;
    -moz-transition: all 0.6s linear;
    -o-transition: all 0.6s linear;
    -ms-transition: all 0.6s linear;
    transition: all 0.6s linear;

    -webkit-animation-fill-mode: none;
	-moz-animation-fill-mode: none;
	-ms-animation-fill-mode: none;
	animation-fill-mode: none;
}
.video-portfolio .owl-carousel .owl-item.active.center .portfolio-box{
	position: relative;
	z-index: 9;
	opacity: 1;
	-webkit-transform: scale(1.5);
	-moz-transform: scale(1.5);
	-ms-transform: scale(1.5);
	transform: scale(1.5);
	-webkit-transition: all 0.6s linear;
    -moz-transition: all 0.6s linear;
    -o-transition: all 0.6s linear;
    -ms-transition: all 0.6s linear;
    transition: all 0.6s linear;

    -webkit-animation-fill-mode: none;
	-moz-animation-fill-mode: none;
	-ms-animation-fill-mode: none;
	animation-fill-mode: none;

	box-shadow: 0 0 50px 6px rgba(0,0,0,0.6);
}


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	PORTFOLIO-1 PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
#portfolio-2 .full-width{ width: 100%; padding: 0 0; }
.portfolio-2-wrp{ padding: 30px; }

/*  PORT2-BOX
---------------------------------*/
.port2-box{ margin: 1px; }
.port2-box img{ display: block; }
.port2-box .port-detail{
	position: absolute;
	top: 20px;
	bottom: 20px;
	left: 20px;
	right: 20px;
	background-color: rgba(255,255,255,0.8);
	opacity: 0;
}
.port2-box:hover .port-detail{ opacity: 1; }
.port-detail .tools .tool,
.port-detail .tools .tool:hover i{
	-webkit-transition: all 0.4s linear;
    -moz-transition: all 0.4s linear;
    -o-transition: all 0.4s linear;
    -ms-transition: all 0.4s linear;
    transition: all 0.4s linear;
}
.port-detail .tools{
	width: 100%;
	text-align: center;
}
.port-detail .tools .tool{
	display: inline-block;
	width: 80px;
	height: 100px;
	color: #000;
	border-right: 1px solid #000;
}
.port-detail .tools .tool:focus{ outline: none; }
.port-detail .tools .tool > span{ display: inline-block; }
.port-detail .tools .tool i{
	font-size: 30px;
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
}
.port-detail .tools .tool:hover i{
	-webkit-transform: scale(1.2);
	-webkit-transform: scale(1.2);
	-webkit-transform: scale(1.2);
	transform: scale(1.2);
}
.port-detail .tools .tool:last-child{ border-right: none; margin-left: -2px; }
.port-detail .tools .zoom{
	-webkit-animation-delay: 0.2s;
    -moz-animation-delay: 0.2s;
    -ms-animation-delay: 0.2s;
    animation-delay: 0.2s;
}
.port-detail .tools .link{
	-webkit-animation-delay: 0.3s;
    -moz-animation-delay: 0.3s;
    -ms-animation-delay: 0.3s;
    animation-delay: 0.3s;
}

@-webkit-keyframes portfadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 20px, 0);
		transform: translate3d(0, 20px, 0);	
	}

	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
@-moz-keyframes portfadeInUp {
	0% {
		opacity: 0;
		-moz-transform: translate3d(0, 20px, 0);
		transform: translate3d(0, 20px, 0);	
	}

	100% {
		opacity: 1;
		-moz-transform: none;
		transform: none;
	}
}
@keyframes portfadeInUp {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0, 20px, 0);
		transform: translate3d(0, 20px, 0);
	}

	100% {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}
.port2-box:hover .port-detail .tools .tool{
	-webkit-animation-name: portfadeInUp;
  	animation-name: portfadeInUp;
  	-webkit-animation-duration: 0.6s;
	animation-duration: 0.6s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}

.mfp-zoom-in {}
.mfp-zoom-in .mfp-with-anim {
	opacity: 0;
	transition: all 0.2s ease-in-out;
	transform: scale(0.8);
}
.mfp-zoom-in.mfp-bg { opacity: 0; transition: all 0.3s ease-out; }
.mfp-zoom-in.mfp-ready .mfp-with-anim { opacity: 1; transform: scale(1); }
.mfp-zoom-in.mfp-ready.mfp-bg { opacity: 0.8; }
.mfp-zoom-in.mfp-removing .mfp-with-anim { transform: scale(0.8); opacity: 0; }
.mfp-zoom-in.mfp-removing.mfp-bg { opacity: 0; }


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	PRICE PAGE
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
.package-box-carousel .carousel .item{
	padding-left: 15px;
	padding-right: 15px;
}
.package-box-carousel .carousel-btn{ display: none; }

/*  PRICE-BOX
---------------------------------*/
.package-box{
	background-color: rgba(255,255,255,0.05);
	text-align: center;
	cursor: pointer;
	border-bottom: 3px solid;
	border-color: transparent;
}
.package-box .top-wrp{
	background-color: rgba(255,255,255,0.05);
	padding: 30px;
}
.package-box .name{ 
	text-transform: uppercase; 
	margin-bottom: 20px;
}
.package-box .name .title{
	font-weight: 700;
	letter-spacing: 3px;
}
.package-box .amount-wrp .amount{
	font-weight: 400;
	font-size: 70px;
	display: block;
	margin-bottom: 5px;
}
.package-box .amount-wrp .other{
	display: block;
	font-style: italic;
	font-weight: 300;
	color: rgba(255,255,255,0.5);
	font-size: 12px;
	line-height: 1.2;
	display: inline-block;
}
.package-box .bottom-wrp{ 
	padding: 30px; 
}
.package-box .detail{ display: block; }
.package-box .detail li{
	border-bottom: 1px solid;
	border-color: rgba(255,255,255,0.2);
	padding: 10px;
	letter-spacing: 1px;
}
.package-box .btn{ 
	display: block; 
	margin-top: 20px;
}
.package-box:hover{
	border-color: #fff;
}