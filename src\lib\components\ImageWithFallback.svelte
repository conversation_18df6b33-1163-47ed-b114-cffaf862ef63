<script lang="ts">
  export let src: string;
  export let alt: string;
  export let fallbackText: string = 'Logo';
  export let className: string = '';

  let imageError = false;

  function handleLoad() {
    imageError = false;
  }

  function handleError() {
    imageError = true;
  }
</script>

{#if !imageError}
  <img
    {src}
    {alt}
    class={className}
    on:load={handleLoad}
    on:error={handleError}
  />
{:else}
  <div class="fallback-logo {className}">
    <div class="fallback-text">{fallbackText}</div>
  </div>
{/if}

<style>
  .fallback-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    width: 100%;
    height: 80px;
    min-height: 80px;
  }

  .fallback-text {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 700;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
  }

  img {
    width: 100%;
    height: auto;
    max-width: 100%;
  }
</style>
