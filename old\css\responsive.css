/*
++++++++++++++++++++++++++++++++++++++++++++++++++++++
[ RESPONSIVE STYLESHEET ]
AUTHOR : NCode.Art
PROJECT : NC-Hold Coming-Soon Page
VERSION : 2.0
++++++++++++++++++++++++++++++++++++++++++++++++++++++
*/


/*......................................................
	DESKTOP LAYOUT
........................................................*/
@media only screen and (min-width: 1500px){

}
@media only screen and (min-width: 1200px) and (max-width: 1499px){

	/*  PORTFOLIO BOX
	---------------------------------*/
	@-webkit-keyframes move-origin {
		0% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
		100% {
			opacity: 1;
			-webkit-transform: scale(1.5);
		}
	}
	@-moz-keyframes move-origin {
		0% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
		100% {
			opacity: 1;
			-moz-transform: scale(1.5);
		}
	}
	@-ms-keyframes move-origin {
		0% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
		100% {
			opacity: 1;
			-ms-transform: scale(1.5);
		}
	}
	@keyframes move-origin {
	  0% {
	  	opacity: 0.2;
	    transform: scale(1);
	  }
	  100% {
	  	opacity: 1;
	    transform: scale(1.5);
	  }
	}

	@-webkit-keyframes move-origin1 {
		0% {
			opacity: 1;
			-webkit-transform: scale(1.5);
		}
		100% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
	}
	@-moz-keyframes move-origin1 {
		0% {
			opacity: 1;
			-moz-transform: scale(1.5);
		}
		100% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
	}
	@-ms-keyframes move-origin1 {
		0% {
			opacity: 1;
			-ms-transform: scale(1.5);
		}
		100% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
	}
	@keyframes move-origin1 {
		0% {
			opacity: 1;
			transform: scale(1.5);
		}

		100% {
			opacity: 0.2;
			transform: scale(1);
		}
	}

}
@media only screen and (min-width: 980px) and (max-width: 1199px){

	/*	GENERAL
	------------------------------*/
	.container{ width: 930px; }

	/*	HOME PAGE
	------------------------------*/
	.tagline p{ width: 80%; }

	/*	CONTACT PAGE
	------------------------------*/
	.contact-box .text{ width: 80%; }

	/*  PORTFOLIO BOX
	---------------------------------*/
	.portfolio-detail .h-text{ font-size: 18px; }
	.portfolio-detail{ padding: 20px; }
	@-webkit-keyframes move-origin {
		0% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
		100% {
			opacity: 1;
			-webkit-transform: scale(1.4);
		}
	}
	@-moz-keyframes move-origin {
		0% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
		100% {
			opacity: 1;
			-moz-transform: scale(1.4);
		}
	}
	@-ms-keyframes move-origin {
		0% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
		100% {
			opacity: 1;
			-ms-transform: scale(1.4);
		}
	}
	@keyframes move-origin {
	  0% {
	  	opacity: 0.2;
	    transform: scale(1);
	  }
	  100% {
	  	opacity: 1;
	    transform: scale(1.4);
	  }
	}

	@-webkit-keyframes move-origin1 {
		0% {
			opacity: 1;
			-webkit-transform: scale(1.4);
		}
		100% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
	}
	@-moz-keyframes move-origin1 {
		0% {
			opacity: 1;
			-moz-transform: scale(1.4);
		}
		100% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
	}
	@-ms-keyframes move-origin1 {
		0% {
			opacity: 1;
			-ms-transform: scale(1.4);
		}
		100% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
	}
	@keyframes move-origin1 {
		0% {
			opacity: 1;
			transform: scale(1.4);
		}

		100% {
			opacity: 0.2;
			transform: scale(1);
		}
	}
}



/*......................................................
	TABLET LAYOUT
........................................................*/
@media only screen and (min-width: 768px) and (max-width: 979px){

	/*	GENERAL
	------------------------------*/
	.container{ width: 715px; }
	.title-wrapper{ margin-bottom: 50px; }

	/*	HOME PAGE
	------------------------------*/
	.logo{ margin-bottom: 40px; }
	.logo .logo-wrp{ width: 100px; }
	.tagline h1{ font-size: 42px; }
	.tagline p{ width: 80%; }
	.social-icon{ margin-top: 40px; }
	.copyrights{ bottom: -40px; }

	/*	ABOUT PAGE
	------------------------------*/
	.measure-box{ width: 90%; }

	/*	ABOUT PAGE STYLE-1
	------------------------------*/
	.large-text{ font-size: 22px; text-align: center; }
	.info-box-1-wrp{ border-left: none; margin-top: 30px; }
	.info-box-1-wrp > .mb{ margin-bottom: 0px; }
	.info-box-1{ margin-bottom: 30px; }

	/*	TIME PAGE
	------------------------------*/
	.dash{ min-width: 117px; }
	.dash .digit{ font-size: 70px; }

	/*	CONTACT PAGE
	------------------------------*/
	.contact-box .text{ width: 75%; padding: 0 15px; }

	/*	OUR-TEAM
	------------------------------*/
	.team-box{ width: 70%; margin: auto; }

	/*  PORTFOLIO BOX
	---------------------------------*/
	.portfolio-box{ max-width: 700px; }
	.portfolio-detail .h-text{ font-size: 16px; }
	.portfolio-detail p{ font-size: 10px; }
	.portfolio-detail{ padding: 20px; }
	.portfolio-carousel .carousel-btn .btn{ width: 60px; }
	.portfolio-carousel .carousel-btn .btn i{ font-size: 50px; }
	@-webkit-keyframes move-origin {
		0% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
		100% {
			opacity: 1;
			-webkit-transform: scale(1.3);
		}
	}
	@-moz-keyframes move-origin {
		0% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
		100% {
			opacity: 1;
			-moz-transform: scale(1.3);
		}
	}
	@-ms-keyframes move-origin {
		0% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
		100% {
			opacity: 1;
			-ms-transform: scale(1.3);
		}
	}
	@keyframes move-origin {
	  0% {
	  	opacity: 0.2;
	    transform: scale(1);
	  }
	  100% {
	  	opacity: 1;
	    transform: scale(1.3);
	  }
	}

	@-webkit-keyframes move-origin1 {
		0% {
			opacity: 1;
			-webkit-transform: scale(1.3);
		}
		100% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
	}
	@-moz-keyframes move-origin1 {
		0% {
			opacity: 1;
			-moz-transform: scale(1.3);
		}
		100% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
	}
	@-ms-keyframes move-origin1 {
		0% {
			opacity: 1;
			-ms-transform: scale(1.3);
		}
		100% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
	}
	@keyframes move-origin1 {
		0% {
			opacity: 1;
			transform: scale(1.3);
		}

		100% {
			opacity: 0.2;
			transform: scale(1);
		}
	}

	/*	VIDEO-PORTFOLIO PAGE
	---------------------------------*/
	.video-portfolio .owl-carousel .owl-item.active.center .portfolio-box{
		-webkit-transform: scale(1.3);
		-moz-transform: scale(1.3);
		-ms-transform: scale(1.3);
		transform: scale(1.3);
	}

	/*  PACKAGE
	---------------------------------*/
	.package-box-carousel .carousel-btn{ display: block; }
}



/*......................................................
	MOBILE LAYOUT
........................................................*/
@media only screen and (min-width: 600px) and (max-width: 767px){

	/*	GENERAL
	------------------------------*/
	.container{ width: 550px; }
	.title-wrapper{ margin-bottom: 40px; }

	/*	HOME PAGE
	------------------------------*/
	.logo{ margin-bottom: 40px; }
	.logo .logo-wrp{ width: 100px; }
	.tagline h1{ font-size: 42px; }
	.tagline p{ width: 80%; }
	.social-icon{ margin-top: 40px; }
	.copyrights{ bottom: -40px; }

	/*	ABOUT PAGE
	------------------------------*/
	.measure-box{ width: 90%; }

	/*	ABOUT PAGE STYLE-1
	------------------------------*/
	.large-text{ font-size: 20px; }

	/*	TIME PAGE
	------------------------------*/
	.dash{ min-width: 117px; padding: 20px 20px 34px 20px; }
	.dash .digit{ 
		font-size: 50px; 
		min-width: 30px; 
	}

	/*	CONTACT PAGE
	------------------------------*/
	.contact-box .text{ width: 75%; padding: 0 15px; }

	/*  PORTFOLIO BOX
	---------------------------------*/
	.portfolio-detail .h-text{ font-size: 18px; }
	.portfolio-detail{ padding: 20px; }

}
@media only screen and (min-width: 480px) and (max-width: 599px){

	/*	GENERAL
	------------------------------*/
	.container{ width: 430px; }
	.title-wrapper{ margin-bottom: 40px; }
	.title-wrapper p{ width: 100%; font-size: 18px; }
	.nc-menu-trigger{ top: 10px; right: 10px; }

	/*	HOME PAGE
	------------------------------*/
	.logo{ margin-bottom: 40px; }
	.logo .logo-wrp{ width: 100px; }
	.tagline h1{ font-size: 40px; }
	.tagline p{ width: 100%; font-size: 18px; }
	.subscribe{ width: auto; }
	.social-icon{ margin-top: 40px; }
	.copyrights{ bottom: -40px; }

	/*	ABOUT PAGE
	------------------------------*/
	.measure-box{ width: 90%; }

	/*	ABOUT PAGE STYLE-1
	------------------------------*/
	.large-text{ font-size: 20px; }

	/*	TIME PAGE
	------------------------------*/
	.dash{ min-width: 90px; padding: 20px 10px 34px 10px; }
	.dash .digit{ 
		font-size: 40px; 
		min-width: 25px; 
	}

	/*	CONTACT PAGE
	------------------------------*/
	.contact-box .text{ width: 75%; padding: 0 15px; }

	/*  PORTFOLIO BOX
	---------------------------------*/
	.portfolio-detail .h-text{ font-size: 16px; }
	.portfolio-detail{ padding: 15px; }

}
@media only screen and (min-width: 320px) and (max-width: 479px){

	/*	GENERAL
	------------------------------*/
	.container{ width: 280px; }
	.title-wrapper{ margin-bottom: 30px; }
	.title-wrapper .title{ font-size: 28px; letter-spacing: 3px; }
	.title-wrapper.sub .title{ font-size: 20px; }
	.title-wrapper .line{ margin-top: 15px; }
	.title-wrapper p{ width: 100%; font-size: 16px; }
	.nc-menu-container{ width: 100%; }
	.nc-menu-trigger{ top: 10px; right: 10px; }
	.nc-menu-container .nav-header{ height: 50px; }
	.nc-menu-close::after, .nc-menu-close::before{ width: 26px; }
	.nc-menu li{ height: 100px; min-height: 100px; }
	.link-box .icon{ 
		width: 35px; 
		height: 35px; 
		margin-bottom: 8px;
	}
	.link-box .icon i{ font-size: 30px; }
	.link-box .text{ font-size: 14px; letter-spacing: 2px; }

	/*	HOME PAGE
	------------------------------*/
	.logo{ margin-bottom: 30px; }
	.logo .logo-wrp{ width: 80px; }
	.tagline{ margin-bottom: 30px; }
	.tagline h1{ font-size: 28px; margin-bottom: 20px; }
	.tagline p{ width: 100%; font-size: 16px; }
	.subscribe{ width: auto; }
	.subscribe .icon{ top: -3px; }
	.subscribe .icon i{ font-size: 28px; }
	.subscribe .form-control{ font-size: 18px; }
	.subscribe .form-control::-webkit-input-placeholder { font-size: 16px; }
	.subscribe .form-control:-moz-placeholder { font-size: 16px; }
	.subscribe .form-control::-moz-placeholder { font-size: 16px; }
	.subscribe .form-control:-ms-input-placeholder { font-size: 16px; }
	.social-icon{ margin-top: 40px; }
	.social-icon .icon{ width: 30px; height: 30px; }
	.copyrights{ 
		position: inherit;
		bottom: 0;
		margin-top: 20px;
	}

	/*	ABOUT PAGE
	------------------------------*/
	.measure-box{ width: 90%; }

	/*	ABOUT PAGE STYLE-1
	------------------------------*/
	.large-text{ font-size: 18px; }

	/*	TIME PAGE
	------------------------------*/
	.dash{ 
		width: 48.3%; 
		min-width: 0;
		float: left;
		padding: 20px 10px 34px 10px; 
	}
	.dash .digit{ 
		font-size: 40px; 
		min-width: 25px; 
	}

	/*	CONTACT PAGE
	------------------------------*/
	.contact-box .text{ width: 75%; padding: 0 15px; }

	/*  PORTFOLIO BOX
	---------------------------------*/
	.portfolio-detail .h-text{ font-size: 16px; }
	.portfolio-detail{ padding: 15px; }

	/*	PORTFOLIO-1 PAGE
	------------------------------*/
	.port-detail .tools .tool{ width: 60px; height: 75px; }
	.port-detail .tools .tool i{ font-size: 24px; }

} 
@media only screen and (min-width: 320px) and (max-width: 767px){

	/*	GENERAL
	------------------------------*/
	.nc-content-section.vhm > .vhm-item{
		position: absolute;
		top: 0;
		width: 100%;
		left: 0;
		-ms-transform: none;
	    -webkit-transform: none;
	    -moz-transform: none;
	    transform: none;
	    padding: 100px 0 80px 0;
	}

	/*	ABOUT PAGE
	------------------------------*/
	.abt-img-wrp{ margin-bottom: 30px; }

	/*	ABOUT PAGE STYLE-1
	------------------------------*/
	.large-text{ text-align: center; }
	.info-box-1-wrp{ border-left: none; margin-top: 30px; }
	.info-box-1-wrp > .mb{ margin-bottom: 0px; }
	.info-box-1{ margin-bottom: 30px; }
	

	/*	CONTACT PAGE
	------------------------------*/
	.contact-form .input-area{ margin-bottom: 15px; }
	.contact-box-wrp{ margin-bottom: 30px; }
	.contact-box{ margin-bottom: 10px; }

	/*  PORTFOLIO BOX
	---------------------------------*/
	#ajax-page.port-full{ height: auto; }
	.portfolio-carousel{ overflow: visible; }
	.portfolio-carousel, .portfolio-carousel .carousel, .portfolio-carousel .owl-carousel{
		position: initial;
	}
	.portfolio-carousel .owl-carousel .owl-stage{ margin: 0; }
	.portfolio-carousel .owl-carousel .owl-stage-outer{
		height: initial !important;
	    transform: none;
	    top: initial;
	    padding: 0;
	}
	.portfolio-carousel .carousel-btn .btn{
		position: relative;
		top: inherit;
		margin-top: 0;
		height: 50px;
		width: 50px;
		background-color: transparent;
	}
	.portfolio-carousel .carousel-btn .btn i{ font-size: 30px; }
	.portfolio-carousel .carousel-btn .prev{ left: 2.5px; }
	.portfolio-carousel .carousel-btn .next{ right: 2.5px; }
	@-webkit-keyframes move-origin {
		0% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
		100% {
			opacity: 1;
			-webkit-transform: scale(1);
		}
	}
	@-moz-keyframes move-origin {
		0% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
		100% {
			opacity: 1;
			-moz-transform: scale(1);
		}
	}
	@-ms-keyframes move-origin {
		0% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
		100% {
			opacity: 1;
			-ms-transform: scale(1);
		}
	}
	@keyframes move-origin {
	  0% {
	  	opacity: 0.2;
	    transform: scale(1);
	  }
	  100% {
	  	opacity: 1;
	    transform: scale(1);
	  }
	}

	@-webkit-keyframes move-origin1 {
		0% {
			opacity: 1;
			-webkit-transform: scale(1);
		}
		100% {
			opacity: 0.2;
			-webkit-transform: scale(1);
		}
	}
	@-moz-keyframes move-origin1 {
		0% {
			opacity: 1;
			-moz-transform: scale(1);
		}
		100% {
			opacity: 0.2;
			-moz-transform: scale(1);
		}
	}
	@-ms-keyframes move-origin1 {
		0% {
			opacity: 1;
			-ms-transform: scale(1);
		}
		100% {
			opacity: 0.2;
			-ms-transform: scale(1);
		}
	}
	@keyframes move-origin1 {
		0% {
			opacity: 1;
			transform: scale(1);
		}

		100% {
			opacity: 0.2;
			transform: scale(1);
		}
	}

	/*	VIDEO-PORTFOLIO PAGE
	---------------------------------*/
	.video-portfolio .owl-carousel .owl-item.active.center .portfolio-box{
		-webkit-transform: scale(1);
		-moz-transform: scale(1);
		-ms-transform: scale(1);
		transform: scale(1);
	}

	/*  PACKAGE
	---------------------------------*/
	.package-box-carousel .carousel-btn{ display: block; }

	/*  TEAM-BOX-1
	---------------------------------*/
	.team-box-1{ width: 75%; margin: auto; }

}