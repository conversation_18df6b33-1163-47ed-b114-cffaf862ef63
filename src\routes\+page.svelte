<script lang="ts">
  import { onMount } from 'svelte';
  import PageLoader from '$lib/components/PageLoader.svelte';
  import ParticlesBackground from '$lib/components/ParticlesBackground.svelte';
  import Logo from '$lib/components/Logo.svelte';
  import MultiLanguageCarousel from '$lib/components/MultiLanguageCarousel.svelte';
  import SocialIcons from '$lib/components/SocialIcons.svelte';
  import Footer from '$lib/components/Footer.svelte';
  import { siteConfig } from '$lib/config.js';

  let isLoading = true;
  let mainContainer: HTMLElement;

  onMount(() => {
    // 动态设置背景图片，如果失败则使用渐变背景
    if (mainContainer) {
      const img = new Image();
      img.onload = () => {
        mainContainer.style.backgroundImage = `url('${siteConfig.background.image}')`;
      };
      img.onerror = () => {
        mainContainer.style.background = siteConfig.background.fallbackColor;
      };
      img.src = siteConfig.background.image;
    }
  });
</script>

<svelte:head>
  <title>{siteConfig.title}</title>
  <meta name="description" content={siteConfig.description} />
  <meta name="keywords" content={siteConfig.keywords} />
  <meta name="author" content={siteConfig.author} />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content={siteConfig.og.type} />
  <meta property="og:url" content={siteConfig.og.url} />
  <meta property="og:title" content={siteConfig.og.title} />
  <meta property="og:description" content={siteConfig.og.description} />
  <meta property="og:image" content={siteConfig.og.url + siteConfig.og.image} />
  <meta property="og:image:alt" content={siteConfig.og.imageAlt} />
  <meta property="og:site_name" content={siteConfig.og.siteName} />
  <meta property="og:locale" content={siteConfig.og.locale} />

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:url" content={siteConfig.og.url} />
  <meta name="twitter:title" content={siteConfig.og.title} />
  <meta name="twitter:description" content={siteConfig.og.description} />
  <meta name="twitter:image" content={siteConfig.og.url + siteConfig.og.image} />
  <meta name="twitter:image:alt" content={siteConfig.og.imageAlt} />

  <!-- Additional SEO -->
  <meta name="robots" content="index, follow" />
  <meta name="googlebot" content="index, follow" />
  <link rel="canonical" href={siteConfig.og.url} />

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
</svelte:head>

<PageLoader bind:isLoading />

<main bind:this={mainContainer} class="main-container" class:loaded={!isLoading}>
  <div class="background-overlay" style="background-color: {siteConfig.background.overlay}"></div>
  <ParticlesBackground />

  <div class="content-wrapper">
    <div class="container">
      <Logo
        logoSrc={siteConfig.logo.src}
        logoAlt={siteConfig.logo.alt}
        href={siteConfig.logo.href}
        target={siteConfig.logo.target}
      />
      <MultiLanguageCarousel messages={siteConfig.welcomeMessages} />
      <SocialIcons socialLinks={siteConfig.socialLinks} />
      <Footer />
    </div>
  </div>
</main>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
    background-color: #000;
    color: #fff;
    overflow-x: hidden;
  }

  :global(*) {
    box-sizing: border-box;
  }

  .main-container {
    position: relative;
    min-height: 100vh;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
  }

  .main-container.loaded {
    opacity: 1;
  }

  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 0;
  }

  .content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 40px 20px;
  }

  .container {
    max-width: 1170px;
    margin: 0 auto;
    text-align: center;
  }

  @media (max-width: 768px) {
    .container {
      max-width: 600px;
    }

    .content-wrapper {
      padding: 20px 15px;
    }
  }

  @media (max-width: 480px) {
    .container {
      max-width: 320px;
    }

    .content-wrapper {
      padding: 15px 10px;
    }
  }
</style>
