/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Microsoft Yahei', '<PERSON>o', sans-serif;
  background-color: #000;
  color: #fff;
  overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 链接样式 */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* 按钮样式重置 */
button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 响应式容器 */
.container {
  width: 100%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
}

@media (max-width: 768px) {
  .container {
    max-width: 600px;
    padding: 0 10px;
  }
}

@media (max-width: 480px) {
  .container {
    max-width: 320px;
    padding: 0 5px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
