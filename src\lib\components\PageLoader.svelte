<script lang="ts">
  import { onMount } from 'svelte';

  export let isLoading = true;

  onMount(() => {
    // 模拟加载时间
    setTimeout(() => {
      isLoading = false;
    }, 800);
  });
</script>

{#if isLoading}
  <div class="page-loader-wrapper">
    <div class="loader"></div>
  </div>
{/if}

<style>
  .page-loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loader {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
