<script lang="ts">
  import TypewriterEffect from '$lib/components/TypewriterEffect.svelte';
  import MouseFollowParticles from '$lib/components/MouseFollowParticles.svelte';
  
  let demoTexts = [
    'Hello, InitCool!',
    '欢迎来到我的个人主页',
    'Welcome to my homepage',
    'This is a typewriter effect demo'
  ];
  
  let currentTextIndex = 0;
  let key = 0;
  
  function nextText() {
    currentTextIndex = (currentTextIndex + 1) % demoTexts.length;
    key++;
  }
  
  function resetDemo() {
    currentTextIndex = 0;
    key++;
  }
</script>

<svelte:head>
  <title>特效演示 - InitCool</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
</svelte:head>

<MouseFollowParticles />

<div class="demo-page">
  <div class="demo-container">
    <h1>✨ 特效演示页面</h1>
    
    <!-- 打字机效果演示 -->
    <section class="demo-section">
      <h2><i class="fa-solid fa-keyboard"></i> 打字机效果</h2>
      <div class="typewriter-demo">
        <div class="demo-text">
          {#key key}
            <TypewriterEffect 
              text={demoTexts[currentTextIndex]} 
              speed={100}
              delay={500}
              showCursor={true}
              cursorChar="|"
            />
          {/key}
        </div>
        
        <div class="demo-controls">
          <button on:click={nextText} class="btn primary">
            <i class="fa-solid fa-forward"></i> 下一个文本
          </button>
          <button on:click={resetDemo} class="btn secondary">
            <i class="fa-solid fa-refresh"></i> 重置演示
          </button>
        </div>
        
        <div class="demo-info">
          <p><strong>当前文本:</strong> {demoTexts[currentTextIndex]}</p>
          <p><strong>文本索引:</strong> {currentTextIndex + 1} / {demoTexts.length}</p>
        </div>
      </div>
    </section>
    
    <!-- 鼠标跟随粒子演示 -->
    <section class="demo-section">
      <h2><i class="fa-solid fa-mouse-pointer"></i> 鼠标跟随粒子</h2>
      <div class="particles-demo">
        <div class="demo-area">
          <p>移动鼠标查看粒子跟随效果</p>
          <div class="mouse-area">
            <div class="instruction">
              <i class="fa-solid fa-hand-pointer"></i>
              <span>在这个区域移动鼠标</span>
            </div>
          </div>
        </div>
        
        <div class="demo-info">
          <h4>特效说明:</h4>
          <ul>
            <li>🎨 多彩粒子效果</li>
            <li>✨ 发光阴影效果</li>
            <li>🌟 粒子生命周期动画</li>
            <li>🎯 智能粒子数量控制</li>
            <li>💫 随机散布效果</li>
          </ul>
        </div>
      </div>
    </section>
    
    <!-- 组合效果演示 -->
    <section class="demo-section">
      <h2><i class="fa-solid fa-magic"></i> 组合效果</h2>
      <div class="combined-demo">
        <div class="hero-section">
          <h3>
            <TypewriterEffect 
              text="InitCool's Amazing Effects" 
              speed={120}
              delay={200}
              showCursor={true}
            />
          </h3>
          <p>
            <TypewriterEffect 
              text="打字机效果 + 鼠标跟随粒子 = 完美的交互体验" 
              speed={80}
              delay={2000}
              showCursor={false}
            />
          </p>
        </div>
      </div>
    </section>
    
    <!-- 返回主页 -->
    <section class="demo-section">
      <div class="navigation">
        <a href="/" class="btn large">
          <i class="fa-solid fa-home"></i> 返回主页
        </a>
      </div>
    </section>
  </div>
</div>

<style>
  .demo-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    font-family: 'Microsoft Yahei', Arial, sans-serif;
    padding: 40px 20px;
  }
  
  .demo-container {
    max-width: 1000px;
    margin: 0 auto;
  }
  
  h1 {
    text-align: center;
    font-size: 48px;
    margin-bottom: 50px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  
  .demo-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .demo-section h2 {
    font-size: 28px;
    margin-bottom: 25px;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .demo-section h2 i {
    color: #64b5f6;
  }
  
  /* 打字机演示样式 */
  .typewriter-demo {
    text-align: center;
  }
  
  .demo-text {
    font-size: 36px;
    font-weight: 700;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 20px;
  }
  
  .demo-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
  }
  
  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
  }
  
  .btn.primary {
    background: #64b5f6;
    color: white;
  }
  
  .btn.primary:hover {
    background: #42a5f5;
    transform: translateY(-2px);
  }
  
  .btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
  }
  
  .btn.large {
    padding: 16px 32px;
    font-size: 18px;
  }
  
  /* 粒子演示样式 */
  .particles-demo {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
  }
  
  .demo-area {
    text-align: center;
  }
  
  .mouse-area {
    width: 100%;
    height: 200px;
    background: rgba(0, 0, 0, 0.2);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    transition: all 0.3s ease;
  }
  
  .mouse-area:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(0, 0, 0, 0.3);
  }
  
  .instruction {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.7);
  }
  
  .instruction i {
    font-size: 24px;
    animation: bounce 2s infinite;
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
  
  .demo-info {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
  }
  
  .demo-info h4 {
    margin-bottom: 15px;
    color: #64b5f6;
  }
  
  .demo-info ul {
    list-style: none;
    padding: 0;
  }
  
  .demo-info li {
    padding: 5px 0;
    opacity: 0.9;
  }
  
  /* 组合效果样式 */
  .combined-demo {
    text-align: center;
  }
  
  .hero-section {
    padding: 40px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
  }
  
  .hero-section h3 {
    font-size: 42px;
    margin-bottom: 20px;
    min-height: 60px;
  }
  
  .hero-section p {
    font-size: 20px;
    min-height: 30px;
    opacity: 0.9;
  }
  
  /* 导航样式 */
  .navigation {
    text-align: center;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .demo-page {
      padding: 20px 10px;
    }
    
    h1 {
      font-size: 36px;
    }
    
    .demo-section {
      padding: 20px;
    }
    
    .demo-text {
      font-size: 24px;
      min-height: 60px;
    }
    
    .particles-demo {
      grid-template-columns: 1fr;
    }
    
    .demo-controls {
      flex-direction: column;
      align-items: center;
    }
    
    .hero-section h3 {
      font-size: 28px;
    }
    
    .hero-section p {
      font-size: 16px;
    }
  }
</style>
