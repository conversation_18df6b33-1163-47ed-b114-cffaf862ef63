<!DOCTYPE html>
<html>
<head>
    <title>Favicon Test</title>
    <link rel="icon" href="/favicon.ico?v=1" type="image/x-icon" />
    <link rel="shortcut icon" href="/favicon.ico?v=1" type="image/x-icon" />
</head>
<body>
    <h1>Favicon Test Page</h1>
    <p>Check if favicon appears in browser tab</p>
    
    <h2>Direct Links:</h2>
    <ul>
        <li><a href="/favicon.ico" target="_blank">Root favicon.ico</a></li>
        <li><a href="/images/nc-fav.ico" target="_blank">Images folder favicon</a></li>
    </ul>
    
    <h2>Image Test:</h2>
    <p>Favicon as image:</p>
    <img src="/images/nc-fav.ico" alt="Favicon" style="width: 32px; height: 32px;" />
    
    <script>
        // Test if favicon files are accessible
        function testFavicon(url) {
            fetch(url)
                .then(response => {
                    console.log(`${url}: ${response.status} ${response.statusText}`);
                    document.getElementById('results').innerHTML += `<li>${url}: ${response.status} ${response.statusText}</li>`;
                })
                .catch(error => {
                    console.error(`${url}: Error - ${error}`);
                    document.getElementById('results').innerHTML += `<li>${url}: Error - ${error}</li>`;
                });
        }
        
        window.onload = function() {
            testFavicon('/favicon.ico');
            testFavicon('/images/nc-fav.ico');
        };
    </script>
    
    <h2>Test Results:</h2>
    <ul id="results"></ul>
</body>
</html>
