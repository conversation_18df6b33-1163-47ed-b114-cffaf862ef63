#!/bin/bash

# 部署脚本
# 用法: ./scripts/deploy.sh [platform]
# 平台选项: vercel, netlify, github, static

set -e

echo "🚀 开始部署 InitCool 个人主页..."

# 检查参数
PLATFORM=${1:-"static"}

echo "📦 安装依赖..."
pnpm install

echo "🔧 构建项目..."
pnpm build

echo "✅ 构建完成！输出目录: build/"

case $PLATFORM in
  "vercel")
    echo "🌐 部署到 Vercel..."
    if command -v vercel &> /dev/null; then
      vercel --prod
    else
      echo "❌ 请先安装 Vercel CLI: npm i -g vercel"
      exit 1
    fi
    ;;
  
  "netlify")
    echo "🌐 部署到 Netlify..."
    if command -v netlify &> /dev/null; then
      netlify deploy --prod --dir=build
    else
      echo "❌ 请先安装 Netlify CLI: npm i -g netlify-cli"
      exit 1
    fi
    ;;
  
  "github")
    echo "🌐 推送到 GitHub (需要配置 GitHub Actions)..."
    git add .
    git commit -m "Deploy: $(date '+%Y-%m-%d %H:%M:%S')"
    git push origin main
    echo "✅ 已推送到 GitHub，GitHub Actions 将自动部署"
    ;;
  
  "static")
    echo "📁 静态文件已准备完成"
    echo "📂 部署文件位置: $(pwd)/build/"
    echo "📋 部署说明:"
    echo "   1. 将 build/ 文件夹上传到您的服务器"
    echo "   2. 确保服务器配置正确的 MIME 类型"
    echo "   3. 配置 404 回退到 index.html (如需要)"
    ;;
  
  *)
    echo "❌ 未知平台: $PLATFORM"
    echo "支持的平台: vercel, netlify, github, static"
    exit 1
    ;;
esac

echo "🎉 部署流程完成！"
