# 部署指南

## 🎯 问题解决

### 原始错误
```
Could not detect a supported production environment. 
See https://svelte.dev/docs/kit/adapters to learn how to configure your app to run on the platform of your choosing
```

### 解决方案 ✅
安装并配置了 `@sveltejs/adapter-static` 静态适配器

## ⚙️ 配置详情

### 1. 安装静态适配器
```bash
pnpm add -D @sveltejs/adapter-static
```

### 2. 更新 svelte.config.js
```javascript
import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

const config = {
  preprocess: vitePreprocess(),
  kit: {
    adapter: adapter({
      pages: 'build',        // 输出目录
      assets: 'build',       // 静态资源目录
      fallback: undefined,   // 回退页面 (SPA模式)
      precompress: false,    // 预压缩
      strict: true          // 严格模式
    })
  }
};
```

### 3. 添加预渲染配置
创建 `src/routes/+layout.js`:
```javascript
// 启用预渲染，将应用构建为静态网站
export const prerender = true;
```

## 🚀 构建和部署

### 本地构建
```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

### 构建输出
- **输出目录**: `build/`
- **静态文件**: 所有页面预渲染为HTML
- **资源文件**: CSS、JS、图片等静态资源

## 🌐 部署平台

### 1. Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

**vercel.json** (可选):
```json
{
  "buildCommand": "pnpm build",
  "outputDirectory": "build",
  "framework": "sveltekit"
}
```

### 2. Netlify 部署
**netlify.toml**:
```toml
[build]
  command = "pnpm build"
  publish = "build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 3. GitHub Pages 部署
**GitHub Actions** (`.github/workflows/deploy.yml`):
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Build
        run: pnpm build
        
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./build
```

### 4. 传统服务器部署
```bash
# 构建
pnpm build

# 上传 build 文件夹到服务器
scp -r build/* user@server:/var/www/html/

# 或使用 rsync
rsync -av build/ user@server:/var/www/html/
```

## 📁 文件结构

### 构建前
```
src/
├── routes/
│   ├── +layout.js          # 预渲染配置
│   ├── +layout.svelte      # 全局布局
│   ├── +page.svelte        # 主页
│   ├── +error.svelte       # 错误页面
│   ├── og-test/
│   ├── og-image/
│   └── footer-demo/
├── lib/
│   ├── components/         # 组件
│   └── config.js          # 配置文件
├── app.html               # HTML模板
└── app.css               # 全局样式

static/
├── images/               # 静态图片
├── favicon.ico          # 网站图标
└── favicon-test.html    # 测试页面
```

### 构建后
```
build/
├── index.html           # 主页
├── og-test/
│   └── index.html       # OG测试页面
├── og-image/
│   └── index.html       # OG图片生成器
├── footer-demo/
│   └── index.html       # 页脚演示
├── _app/
│   ├── immutable/       # 版本化资源
│   └── version.json     # 版本信息
├── images/              # 静态图片
├── favicon.ico          # 网站图标
└── favicon-test.html    # 测试页面
```

## 🔧 高级配置

### SPA模式 (单页应用)
如果需要SPA模式，修改配置：
```javascript
// src/routes/+layout.js
export const prerender = false;
export const ssr = false;

// svelte.config.js
adapter: adapter({
  fallback: 'index.html'  // 所有路由回退到index.html
})
```

### 自定义404页面
```javascript
// svelte.config.js
adapter: adapter({
  fallback: '404.html'  // 自定义404页面
})
```

### 预压缩
```javascript
// svelte.config.js
adapter: adapter({
  precompress: true  // 启用gzip压缩
})
```

## 🌟 性能优化

### 1. 图片优化
- 使用WebP格式
- 压缩图片大小
- 添加适当的alt属性

### 2. 代码分割
- SvelteKit自动进行代码分割
- 按路由分割JavaScript

### 3. 缓存策略
- 静态资源使用版本化文件名
- 设置适当的缓存头

### 4. CDN配置
```nginx
# Nginx配置示例
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
  expires 1y;
  add_header Cache-Control "public, immutable";
}

location / {
  try_files $uri $uri/ /index.html;
}
```

## 🧪 测试部署

### 本地测试
```bash
# 构建
pnpm build

# 启动预览服务器
pnpm preview

# 访问 http://localhost:4173
```

### 检查清单
- [ ] 所有页面正常加载
- [ ] 静态资源正确引用
- [ ] 图片正常显示
- [ ] 社交分享卡片正常
- [ ] 响应式设计正常
- [ ] 404页面正常

## 🎉 部署完成

现在您的SvelteKit应用已经配置为静态网站，可以部署到任何静态托管平台：

1. **构建成功** ✅ - 生成静态HTML文件
2. **预渲染启用** ✅ - 所有页面预渲染
3. **静态资源优化** ✅ - 自动版本化和压缩
4. **多平台支持** ✅ - 支持各种部署平台

选择您喜欢的部署平台，上传 `build` 文件夹即可！🚀
