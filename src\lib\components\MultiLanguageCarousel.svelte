<script lang="ts">
  import { onMount } from 'svelte';

  interface Message {
    title: string;
    subtitle: string;
  }

  export let messages: Message[] = [
    { title: 'Hello', subtitle: 'Welcome to InitCool\'s Personal Pages!' },
    { title: '你好', subtitle: '欢迎来到 InitCool 的个人主页！' },
    { title: 'Bonjour', subtitle: 'C\'est ma page d\'accueil.' },
    { title: 'Hallo', subtitle: 'Das ist meine Persönliche homepage.' },
    { title: 'こんにちは', subtitle: 'これは私の個人のホームページです.' },
    { title: 'Ciao', subtitle: 'Questa è la mia Pagina personale.' },
    { title: 'Привет', subtitle: 'Это моя личная страница .' }
  ];

  let currentIndex = 0;
  let intervalId: number;

  function nextSlide() {
    currentIndex = (currentIndex + 1) % messages.length;
  }

  function prevSlide() {
    currentIndex = currentIndex === 0 ? messages.length - 1 : currentIndex - 1;
  }

  function goToSlide(index: number) {
    currentIndex = index;
  }

  onMount(() => {
    intervalId = setInterval(nextSlide, 4000);

    return () => {
      clearInterval(intervalId);
    };
  });
</script>

<div class="tagline-carousel">
  <div class="carousel-container">
    <div class="carousel-wrapper" style="transform: translateX(-{currentIndex * 100}%)">
      {#each messages as message, index}
        <div class="carousel-item" class:active={index === currentIndex}>
          <h1>{message.title}</h1>
          <p>{message.subtitle}</p>
        </div>
      {/each}
    </div>
  </div>

  <div class="carousel-dots">
    {#each messages as _, index}
      <button
        class="dot"
        class:active={index === currentIndex}
        on:click={() => goToSlide(index)}
        aria-label="Go to slide {index + 1}"
      ></button>
    {/each}
  </div>

  <button class="carousel-btn prev" on:click={prevSlide} aria-label="Previous slide">
    &#8249;
  </button>
  <button class="carousel-btn next" on:click={nextSlide} aria-label="Next slide">
    &#8250;
  </button>
</div>

<style>
  .tagline-carousel {
    position: relative;
    text-align: center;
    margin-bottom: 60px;
    animation: fadeInUp 1s ease-out 0.7s both;
  }

  .carousel-container {
    overflow: hidden;
    width: 100%;
    height: 120px;
  }

  .carousel-wrapper {
    display: flex;
    transition: transform 0.5s ease-in-out;
    height: 100%;
  }

  .carousel-item {
    flex: 0 0 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 20px;
  }

  .carousel-item h1 {
    font-family: 'Microsoft Yahei', 'Roboto', sans-serif;
    font-size: 48px;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #fff;
    margin: 0 0 10px 0;
  }

  .carousel-item p {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    line-height: 1.4;
  }

  .carousel-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
  }

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: none;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .dot.active {
    background-color: #fff;
  }

  .carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0;
  }

  .tagline-carousel:hover .carousel-btn {
    opacity: 1;
  }

  .carousel-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .prev {
    left: -60px;
  }

  .next {
    right: -60px;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  @media (max-width: 768px) {
    .carousel-item h1 {
      font-size: 36px;
    }

    .carousel-item p {
      font-size: 16px;
    }

    .carousel-container {
      height: 100px;
    }

    .prev {
      left: -50px;
    }

    .next {
      right: -50px;
    }
  }

  @media (max-width: 480px) {
    .carousel-item h1 {
      font-size: 28px;
    }

    .carousel-item p {
      font-size: 14px;
    }

    .carousel-container {
      height: 80px;
    }

    .prev, .next {
      display: none;
    }
  }
</style>
