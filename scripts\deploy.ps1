# PowerShell 部署脚本
# 用法: .\scripts\deploy.ps1 [platform]
# 平台选项: vercel, netlify, github, static

param(
    [string]$Platform = "static"
)

Write-Host "🚀 开始部署 InitCool 个人主页..." -ForegroundColor Green

try {
    Write-Host "📦 安装依赖..." -ForegroundColor Yellow
    pnpm install
    
    Write-Host "🔧 构建项目..." -ForegroundColor Yellow
    pnpm build
    
    Write-Host "✅ 构建完成！输出目录: build/" -ForegroundColor Green
    
    switch ($Platform.ToLower()) {
        "vercel" {
            Write-Host "🌐 部署到 Vercel..." -ForegroundColor Cyan
            if (Get-Command vercel -ErrorAction SilentlyContinue) {
                vercel --prod
            } else {
                Write-Host "❌ 请先安装 Vercel CLI: npm i -g vercel" -ForegroundColor Red
                exit 1
            }
        }
        
        "netlify" {
            Write-Host "🌐 部署到 Netlify..." -ForegroundColor Cyan
            if (Get-Command netlify -ErrorAction SilentlyContinue) {
                netlify deploy --prod --dir=build
            } else {
                Write-Host "❌ 请先安装 Netlify CLI: npm i -g netlify-cli" -ForegroundColor Red
                exit 1
            }
        }
        
        "github" {
            Write-Host "🌐 推送到 GitHub (需要配置 GitHub Actions)..." -ForegroundColor Cyan
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            git add .
            git commit -m "Deploy: $timestamp"
            git push origin main
            Write-Host "✅ 已推送到 GitHub，GitHub Actions 将自动部署" -ForegroundColor Green
        }
        
        "static" {
            Write-Host "📁 静态文件已准备完成" -ForegroundColor Cyan
            Write-Host "📂 部署文件位置: $(Get-Location)\build\" -ForegroundColor White
            Write-Host "📋 部署说明:" -ForegroundColor White
            Write-Host "   1. 将 build\ 文件夹上传到您的服务器" -ForegroundColor White
            Write-Host "   2. 确保服务器配置正确的 MIME 类型" -ForegroundColor White
            Write-Host "   3. 配置 404 回退到 index.html (如需要)" -ForegroundColor White
        }
        
        default {
            Write-Host "❌ 未知平台: $Platform" -ForegroundColor Red
            Write-Host "支持的平台: vercel, netlify, github, static" -ForegroundColor Yellow
            exit 1
        }
    }
    
    Write-Host "🎉 部署流程完成！" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 部署过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
