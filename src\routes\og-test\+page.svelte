<script lang="ts">
  import { siteConfig } from '$lib/config.js';
</script>

<svelte:head>
  <title>OG Meta 测试 - InitCool</title>
</svelte:head>

<div class="og-test">
  <h1>Open Graph Meta 标签测试</h1>
  
  <div class="test-section">
    <h2>当前配置</h2>
    <div class="config-display">
      <div class="config-item">
        <strong>标题:</strong> {siteConfig.og.title}
      </div>
      <div class="config-item">
        <strong>描述:</strong> {siteConfig.og.description}
      </div>
      <div class="config-item">
        <strong>图片:</strong> {siteConfig.og.url + siteConfig.og.image}
      </div>
      <div class="config-item">
        <strong>URL:</strong> {siteConfig.og.url}
      </div>
      <div class="config-item">
        <strong>类型:</strong> {siteConfig.og.type}
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>预览效果</h2>
    <div class="og-preview">
      <div class="og-card">
        <div class="og-image">
          <img src={siteConfig.og.image} alt={siteConfig.og.imageAlt} />
        </div>
        <div class="og-content">
          <h3>{siteConfig.og.title}</h3>
          <p>{siteConfig.og.description}</p>
          <span class="og-url">{siteConfig.og.url}</span>
        </div>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>测试工具</h2>
    <div class="test-tools">
      <div class="tool">
        <h4>Facebook 分享调试器</h4>
        <p>测试Facebook和其他使用OG标签的平台</p>
        <a href="https://developers.facebook.com/tools/debug/" target="_blank" class="btn">
          打开 Facebook Debugger
        </a>
      </div>
      
      <div class="tool">
        <h4>Twitter Card 验证器</h4>
        <p>测试Twitter卡片显示效果</p>
        <a href="https://cards-dev.twitter.com/validator" target="_blank" class="btn">
          打开 Twitter Validator
        </a>
      </div>
      
      <div class="tool">
        <h4>LinkedIn 检查器</h4>
        <p>测试LinkedIn分享效果</p>
        <a href="https://www.linkedin.com/post-inspector/" target="_blank" class="btn">
          打开 LinkedIn Inspector
        </a>
      </div>
      
      <div class="tool">
        <h4>WhatsApp 预览</h4>
        <p>测试WhatsApp链接预览</p>
        <a href="https://developers.facebook.com/tools/debug/" target="_blank" class="btn">
          使用 Facebook Debugger
        </a>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>生成的Meta标签</h2>
    <div class="meta-tags">
      <pre><code>&lt;!-- Open Graph / Facebook --&gt;
&lt;meta property="og:type" content="{siteConfig.og.type}" /&gt;
&lt;meta property="og:url" content="{siteConfig.og.url}" /&gt;
&lt;meta property="og:title" content="{siteConfig.og.title}" /&gt;
&lt;meta property="og:description" content="{siteConfig.og.description}" /&gt;
&lt;meta property="og:image" content="{siteConfig.og.url + siteConfig.og.image}" /&gt;
&lt;meta property="og:image:alt" content="{siteConfig.og.imageAlt}" /&gt;
&lt;meta property="og:site_name" content="{siteConfig.og.siteName}" /&gt;
&lt;meta property="og:locale" content="{siteConfig.og.locale}" /&gt;

&lt;!-- Twitter --&gt;
&lt;meta name="twitter:card" content="summary_large_image" /&gt;
&lt;meta name="twitter:url" content="{siteConfig.og.url}" /&gt;
&lt;meta name="twitter:title" content="{siteConfig.og.title}" /&gt;
&lt;meta name="twitter:description" content="{siteConfig.og.description}" /&gt;
&lt;meta name="twitter:image" content="{siteConfig.og.url + siteConfig.og.image}" /&gt;</code></pre>
    </div>
  </div>
  
  <div class="test-section">
    <h2>使用说明</h2>
    <ol>
      <li>复制您的网站URL</li>
      <li>在上面的测试工具中粘贴URL</li>
      <li>查看预览效果</li>
      <li>如果需要更新，修改配置后重新测试</li>
    </ol>
  </div>
</div>

<style>
  .og-test {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px 20px;
    font-family: 'Microsoft Yahei', Arial, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
  }
  
  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 40px;
  }
  
  .test-section {
    background: white;
    margin-bottom: 30px;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  h2 {
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #2a5298;
    padding-bottom: 10px;
  }
  
  .config-display {
    display: grid;
    gap: 15px;
  }
  
  .config-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #2a5298;
  }
  
  .config-item strong {
    color: #2a5298;
    margin-right: 10px;
  }
  
  .og-preview {
    display: flex;
    justify-content: center;
  }
  
  .og-card {
    max-width: 500px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .og-image {
    width: 100%;
    height: 260px;
    overflow: hidden;
  }
  
  .og-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .og-content {
    padding: 20px;
  }
  
  .og-content h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
  }
  
  .og-content p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .og-url {
    color: #999;
    font-size: 12px;
    text-transform: uppercase;
  }
  
  .test-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .tool {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    text-align: center;
  }
  
  .tool h4 {
    color: #333;
    margin-bottom: 10px;
  }
  
  .tool p {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
  }
  
  .btn {
    display: inline-block;
    padding: 10px 20px;
    background: #2a5298;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s ease;
  }
  
  .btn:hover {
    background: #1e3c72;
  }
  
  .meta-tags {
    background: #f8f9fa;
    border-radius: 4px;
    overflow-x: auto;
  }
  
  pre {
    margin: 0;
    padding: 20px;
  }
  
  code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
  }
  
  ol {
    color: #666;
    line-height: 1.6;
  }
  
  ol li {
    margin-bottom: 8px;
  }
  
  @media (max-width: 768px) {
    .og-test {
      padding: 20px 10px;
    }
    
    .test-section {
      padding: 20px;
    }
    
    .test-tools {
      grid-template-columns: 1fr;
    }
  }
</style>
