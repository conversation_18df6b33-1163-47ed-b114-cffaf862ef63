<script lang="ts">
  import { siteConfig } from '$lib/config.js';
  import { onMount } from 'svelte';
  
  let canvas: HTMLCanvasElement;
  let ctx: CanvasRenderingContext2D;
  
  onMount(() => {
    const context = canvas.getContext('2d');
    if (!context) return;
    ctx = context;
    
    // 设置画布大小 (1200x630 是标准的OG图片尺寸)
    canvas.width = 1200;
    canvas.height = 630;
    
    generateOGImage();
  });
  
  function generateOGImage() {
    // 渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 1200, 630);
    gradient.addColorStop(0, '#1e3c72');
    gradient.addColorStop(1, '#2a5298');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1200, 630);
    
    // 添加粒子效果
    drawParticles();
    
    // 主标题
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 72px "Microsoft Yahei", Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('InitCool', 600, 250);
    
    // 副标题
    ctx.font = '36px "Microsoft Yahei", Arial, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillText('Personal Homepage', 600, 320);
    
    // 描述文字
    ctx.font = '24px "Microsoft Yahei", Arial, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.fillText('欢迎来到我的个人主页', 600, 380);
    
    // 网站URL
    ctx.font = '20px "Courier New", monospace';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('nmslwsnd.com', 600, 450);
    
    // 社交图标区域
    drawSocialIcons();
  }
  
  function drawParticles() {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * 1200;
      const y = Math.random() * 630;
      const size = Math.random() * 3 + 1;
      
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    }
  }
  
  function drawSocialIcons() {
    const iconY = 520;
    const iconSize = 40;
    const iconSpacing = 80;
    const startX = 600 - (4 * iconSpacing) / 2;
    
    // 绘制简单的图标占位符
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    
    const icons = ['Blog', 'GitHub', 'Steam', 'B站'];
    icons.forEach((icon, index) => {
      const x = startX + index * iconSpacing;
      
      // 绘制圆形背景
      ctx.beginPath();
      ctx.arc(x, iconY, iconSize / 2, 0, Math.PI * 2);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 绘制图标文字
      ctx.fillText(icon, x, iconY + 6);
    });
  }
  
  function downloadImage() {
    const link = document.createElement('a');
    link.download = 'og-image.jpg';
    link.href = canvas.toDataURL('image/jpeg', 0.9);
    link.click();
  }
</script>

<svelte:head>
  <title>OG Image Generator - InitCool</title>
</svelte:head>

<div class="og-generator">
  <h1>Open Graph 图片生成器</h1>
  <p>为社交媒体分享生成预览图片</p>
  
  <div class="canvas-container">
    <canvas bind:this={canvas}></canvas>
  </div>
  
  <div class="controls">
    <button on:click={generateOGImage} class="btn primary">重新生成</button>
    <button on:click={downloadImage} class="btn secondary">下载图片</button>
  </div>
  
  <div class="info">
    <h2>使用说明</h2>
    <ol>
      <li>点击"下载图片"保存生成的OG图片</li>
      <li>将图片重命名为 <code>og-image.jpg</code></li>
      <li>放置到 <code>static/images/</code> 文件夹中</li>
      <li>图片尺寸：1200x630px (标准OG图片尺寸)</li>
    </ol>
  </div>
</div>

<style>
  .og-generator {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    font-family: 'Microsoft Yahei', Arial, sans-serif;
    background: #f5f5f5;
    min-height: 100vh;
  }
  
  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 10px;
  }
  
  p {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
  }
  
  .canvas-container {
    text-align: center;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  canvas {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  
  .controls {
    text-align: center;
    margin-bottom: 40px;
    gap: 15px;
    display: flex;
    justify-content: center;
  }
  
  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
  }
  
  .btn.primary {
    background: #2a5298;
    color: white;
  }
  
  .btn.primary:hover {
    background: #1e3c72;
  }
  
  .btn.secondary {
    background: #6c757d;
    color: white;
  }
  
  .btn.secondary:hover {
    background: #545b62;
  }
  
  .info {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .info h2 {
    color: #333;
    margin-bottom: 20px;
  }
  
  .info ol {
    color: #666;
    line-height: 1.6;
  }
  
  .info li {
    margin-bottom: 10px;
  }
  
  code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
  }
  
  @media (max-width: 768px) {
    .og-generator {
      padding: 20px 10px;
    }
    
    .controls {
      flex-direction: column;
      align-items: center;
    }
    
    .btn {
      width: 200px;
    }
  }
</style>
