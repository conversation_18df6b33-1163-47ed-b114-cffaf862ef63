<script lang="ts">
  import { onMount } from 'svelte';
  import { siteConfig } from '$lib/config.js';

  let timeDisplay = '';
  let intervalId: number;

  // 获取当前年份和起始年份
  const currentYear = new Date().getFullYear();
  const startYear = new Date(siteConfig.footer.startDate).getFullYear();
  const copyrightYears = startYear === currentYear ? `${currentYear}` : `${startYear} - ${currentYear}`;

  function updateTime() {
    const startDate = new Date(siteConfig.footer.startDate);
    const now = new Date();
    const timeDiff = now.getTime() - startDate.getTime();

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

    timeDisplay = `${days}D ${hours}H ${minutes}M ${seconds}S`;
  }

  onMount(() => {
    updateTime();
    intervalId = setInterval(updateTime, 1000);

    return () => {
      clearInterval(intervalId);
    };
  });
</script>

<div class="footer">
  <div class="copyright-section">
    <div class="copyright-text">
      {siteConfig.footer.copyrightText}
    </div>

    <div class="copyright-years">
      <i class="fa-regular fa-copyright"></i>
      <span>{copyrightYears} InitCool</span>
    </div>
  </div>

  <div class="time-counter">
    <div class="counter-label">网站运行时间</div>
    <div class="counter-display">
      <span>{timeDisplay}</span>
    </div>
  </div>
</div>

<style>
  .footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 1s both;
    margin-top: 40px;
  }

  .copyright-section {
    margin-bottom: 30px;
  }

  .copyright-text {
    margin-bottom: 15px;
    font-weight: 300;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
  }

  .copyright-years {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
  }

  .copyright-years i {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.6);
  }

  .time-counter {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 16px 20px;
    display: inline-block;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .counter-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
  }

  .counter-display {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    color: #fff;
    font-weight: 600;
    letter-spacing: 2px;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  @media (max-width: 768px) {
    .footer {
      margin-top: 30px;
    }

    .copyright-text {
      font-size: 14px;
    }

    .copyright-years {
      font-size: 12px;
    }

    .time-counter {
      padding: 12px 16px;
    }

    .counter-display {
      font-size: 16px;
      letter-spacing: 1px;
    }
  }

  @media (max-width: 480px) {
    .footer {
      margin-top: 25px;
    }

    .copyright-section {
      margin-bottom: 20px;
    }

    .copyright-text {
      font-size: 13px;
      margin-bottom: 10px;
    }

    .copyright-years {
      font-size: 11px;
      gap: 6px;
    }

    .time-counter {
      padding: 10px 14px;
    }

    .counter-label {
      font-size: 10px;
      margin-bottom: 6px;
    }

    .counter-display {
      font-size: 14px;
      letter-spacing: 1px;
    }
  }
</style>
