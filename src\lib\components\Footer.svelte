<script lang="ts">
  import { onMount } from 'svelte';
  import { siteConfig } from '$lib/config.js';

  let timeDisplay = '';
  let intervalId: number;

  function updateTime() {
    const startDate = new Date(siteConfig.footer.startDate);
    const now = new Date();
    const timeDiff = now.getTime() - startDate.getTime();

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

    timeDisplay = `${days}D ${hours}H ${minutes}M ${seconds}S`;
  }

  onMount(() => {
    updateTime();
    intervalId = setInterval(updateTime, 1000);

    return () => {
      clearInterval(intervalId);
    };
  });
</script>

<div class="footer">
  <div class="copyright-text">
    {siteConfig.footer.copyrightText}
  </div>

  <div class="time-counter">
    <span>{timeDisplay}</span>
  </div>
</div>

<style>
  .footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 1s both;
    margin-top: 40px;
  }

  .copyright-text {
    margin-bottom: 20px;
    font-weight: 300;
  }

  .time-counter {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 4px;
    display: inline-block;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  @media (max-width: 768px) {
    .footer {
      font-size: 12px;
    }

    .time-counter {
      font-size: 14px;
      padding: 6px 12px;
    }
  }

  @media (max-width: 480px) {
    .footer {
      font-size: 11px;
      margin-top: 30px;
    }

    .time-counter {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
</style>
