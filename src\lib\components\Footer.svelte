<script lang="ts">
  import { onMount } from 'svelte';
  import { siteConfig } from '$lib/config.js';

  let timeDisplay = '';
  let intervalId: number;

  // 获取当前年份和起始年份
  const currentYear = new Date().getFullYear();
  const startYear = new Date(siteConfig.footer.startDate).getFullYear();
  const copyrightYears = startYear === currentYear ? `${currentYear}` : `${startYear} - ${currentYear}`;

  function updateTime() {
    const startDate = new Date(siteConfig.footer.startDate);
    const now = new Date();
    const timeDiff = now.getTime() - startDate.getTime();

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

    timeDisplay = `${days}D ${hours}H ${minutes}M ${seconds}S`;
  }

  onMount(() => {
    updateTime();
    intervalId = setInterval(updateTime, 1000);

    return () => {
      clearInterval(intervalId);
    };
  });
</script>

<div class="footer">
  <!-- 格言文字 -->
  <div class="motto-text">
    {siteConfig.footer.copyrightText}
  </div>

  <!-- 运行时间计数器 -->
  <div class="time-counter">
    <div class="counter-label">网站运行时间</div>
    <div class="counter-display">
      <span>{timeDisplay}</span>
    </div>
  </div>

  <!-- 备案信息 (可选) -->
  {#if siteConfig.footer.showBeian && siteConfig.footer.beianNumber}
    <div class="beian-info">
      <a href={siteConfig.footer.beianUrl} target="_blank" rel="noopener noreferrer">
        <i class="fa-solid fa-shield-halved"></i>
        <span>{siteConfig.footer.beianNumber}</span>
      </a>
    </div>
  {/if}

  <!-- 版权年份 (最下方) -->
  <div class="copyright-years">
    <i class="fa-regular fa-copyright"></i>
    <span>{copyrightYears} InitCool. All rights reserved.</span>
  </div>
</div>

<style>
  .footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 1s both;
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
  }

  /* 格言文字 */
  .motto-text {
    font-size: 18px;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    letter-spacing: 1px;
  }

  /* 运行时间计数器 */
  .time-counter {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 16px 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .counter-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
  }

  .counter-display {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    color: #fff;
    font-weight: 600;
    letter-spacing: 2px;
  }

  /* 备案信息 */
  .beian-info {
    margin: 5px 0;
  }

  .beian-info a {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
  }

  .beian-info a:hover {
    color: rgba(255, 255, 255, 0.8);
  }

  .beian-info i {
    font-size: 14px;
  }

  /* 版权年份 (最下方) */
  .copyright-years {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 10px;
    width: 100%;
    max-width: 400px;
  }

  .copyright-years i {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  @media (max-width: 768px) {
    .footer {
      margin-top: 30px;
      gap: 20px;
    }

    .motto-text {
      font-size: 16px;
    }

    .copyright-years {
      font-size: 12px;
      padding-top: 15px;
    }

    .time-counter {
      padding: 12px 16px;
    }

    .counter-display {
      font-size: 16px;
      letter-spacing: 1px;
    }

    .beian-info a {
      font-size: 12px;
    }
  }

  @media (max-width: 480px) {
    .footer {
      margin-top: 25px;
      gap: 18px;
    }

    .motto-text {
      font-size: 14px;
      letter-spacing: 0.5px;
    }

    .copyright-years {
      font-size: 11px;
      gap: 6px;
      padding-top: 12px;
    }

    .time-counter {
      padding: 10px 14px;
    }

    .counter-label {
      font-size: 10px;
      margin-bottom: 6px;
    }

    .counter-display {
      font-size: 14px;
      letter-spacing: 1px;
    }

    .beian-info a {
      font-size: 11px;
      gap: 4px;
    }
  }
</style>
