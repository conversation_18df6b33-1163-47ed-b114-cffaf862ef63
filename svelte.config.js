import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		// 使用静态适配器，适合部署到静态托管平台
		adapter: adapter({
			// 输出目录
			pages: 'build',
			// 静态资源目录
			assets: 'build',
			// 回退页面 (用于SPA模式)
			fallback: undefined,
			// 预压缩
			precompress: false,
			// 严格模式 (所有页面必须可预渲染)
			strict: true
		})
	}
};

export default config;
