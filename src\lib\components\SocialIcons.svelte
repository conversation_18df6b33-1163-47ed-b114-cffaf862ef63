<script lang="ts">
  interface SocialLink {
    icon: string;
    href: string;
    title: string;
    description?: string;
  }

  export let socialLinks: SocialLink[] = [
    { icon: 'fa-user', href: 'https://blog.nmslwsnd.com', title: 'Blog' },
    { icon: 'fa-cloud', href: 'https://nextcloud.nmslwsnd.com', title: 'NextCloud' },
    { icon: 'fa-steam', href: 'https://steamcommunity.com/id/hello-world-', title: 'Steam' },
    { icon: 'fa-image', href: 'https://image.nmslwsnd.com', title: 'Images' }
  ];
</script>

<div class="social-icons">
  {#each socialLinks as link}
    <a
      class="icon"
      href={link.href}
      target="_blank"
      rel="noopener noreferrer"
      title={link.title}
      aria-label={link.title}
    >
      <span class="box"></span>
      <i class="fa {link.icon}"></i>
    </a>
  {/each}
</div>

<style>
  .social-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 40px 0;
    animation: fadeInUp 1s ease-out 0.9s both;
  }

  .icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    text-decoration: none;
    color: #fff;
    font-size: 24px;
    transition: all 0.3s ease;
  }

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transform: rotate(45deg);
    transition: all 0.3s ease;
  }

  .icon i {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
  }

  .icon:hover .box {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.6);
    transform: rotate(45deg) scale(1.1);
  }

  .icon:hover i {
    transform: scale(1.2);
    color: #fff;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  @media (max-width: 768px) {
    .social-icons {
      gap: 15px;
    }

    .icon {
      width: 50px;
      height: 50px;
      font-size: 20px;
    }
  }

  @media (max-width: 480px) {
    .social-icons {
      gap: 12px;
      flex-wrap: wrap;
    }

    .icon {
      width: 45px;
      height: 45px;
      font-size: 18px;
    }
  }
</style>
