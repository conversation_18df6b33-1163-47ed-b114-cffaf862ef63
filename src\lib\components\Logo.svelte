<script lang="ts">
  import ImageWithFallback from './ImageWithFallback.svelte';

  export let logoSrc = '/images/logo.gif';
  export let logoAlt = 'Logo';
  export let href = 'https://blog.nmslwsnd.com';
  export let target = '_blank';
</script>

<div class="logo">
  <div class="logo-wrapper">
    <a {href} {target}>
      <ImageWithFallback src={logoSrc} alt={logoAlt} fallbackText="COOL" />
    </a>
  </div>
</div>

<style>
  .logo {
    text-align: center;
    margin-bottom: 60px;
    animation: fadeInUp 1s ease-out 0.5s both;
  }

  .logo-wrapper {
    width: 120px;
    margin: 0 auto;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  @media (max-width: 768px) {
    .logo {
      margin-bottom: 40px;
    }

    .logo-wrapper {
      width: 100px;
    }
  }

  @media (max-width: 480px) {
    .logo-wrapper {
      width: 80px;
    }
  }
</style>
