<script lang="ts">
  import Footer from '$lib/components/Footer.svelte';
  import { siteConfig } from '$lib/config.js';
  
  // 创建一个可修改的配置副本用于演示
  let demoConfig = {
    ...siteConfig,
    footer: {
      ...siteConfig.footer,
      showBeian: false,
      beianNumber: "京ICP备12345678号-1"
    }
  };
  
  function toggleBeian() {
    demoConfig.footer.showBeian = !demoConfig.footer.showBeian;
    // 触发响应式更新
    demoConfig = { ...demoConfig };
  }
  
  function updateBeianNumber(event: Event) {
    const target = event.target as HTMLInputElement;
    demoConfig.footer.beianNumber = target.value;
    demoConfig = { ...demoConfig };
  }
</script>

<svelte:head>
  <title>页脚演示 - InitCool</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
</svelte:head>

<div class="demo-page">
  <div class="demo-container">
    <h1>📄 页脚组件演示</h1>
    
    <!-- 控制面板 -->
    <section class="control-panel">
      <h2>控制面板</h2>
      
      <div class="control-group">
        <label class="switch-label">
          <input 
            type="checkbox" 
            bind:checked={demoConfig.footer.showBeian}
            on:change={toggleBeian}
          />
          <span class="switch-slider"></span>
          <span class="switch-text">显示备案号</span>
        </label>
      </div>
      
      <div class="control-group">
        <label for="beian-input">备案号:</label>
        <input 
          id="beian-input"
          type="text" 
          bind:value={demoConfig.footer.beianNumber}
          on:input={updateBeianNumber}
          placeholder="请输入备案号，如：京ICP备12345678号-1"
          class="beian-input"
        />
      </div>
      
      <div class="status-info">
        <h3>当前状态:</h3>
        <ul>
          <li><strong>显示备案号:</strong> {demoConfig.footer.showBeian ? '是' : '否'}</li>
          <li><strong>备案号:</strong> {demoConfig.footer.beianNumber || '未设置'}</li>
          <li><strong>版权年份:</strong> 2015 - 2024</li>
        </ul>
      </div>
    </section>
    
    <!-- 页脚预览 -->
    <section class="footer-preview">
      <h2>页脚预览</h2>
      <div class="preview-container">
        <!-- 这里需要创建一个临时的Footer组件来显示演示效果 -->
        <div class="demo-footer">
          <!-- 格言文字 -->
          <div class="motto-text">
            {demoConfig.footer.copyrightText}
          </div>
          
          <!-- 运行时间计数器 -->
          <div class="time-counter">
            <div class="counter-label">网站运行时间</div>
            <div class="counter-display">
              <span>3287D 15H 42M 18S</span>
            </div>
          </div>
          
          <!-- 备案信息 (可选) -->
          {#if demoConfig.footer.showBeian && demoConfig.footer.beianNumber}
            <div class="beian-info">
              <a href={demoConfig.footer.beianUrl} target="_blank" rel="noopener noreferrer">
                <i class="fa-solid fa-shield-halved"></i>
                <span>{demoConfig.footer.beianNumber}</span>
              </a>
            </div>
          {/if}
          
          <!-- 版权年份 (最下方) -->
          <div class="copyright-years">
            <i class="fa-regular fa-copyright"></i>
            <span>2015 - 2024 InitCool. All rights reserved.</span>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 配置说明 -->
    <section class="config-info">
      <h2>配置说明</h2>
      
      <div class="config-example">
        <h3>配置文件示例 (src/lib/config.js):</h3>
        <pre><code>{`footer: {
  copyrightText: "陈力就列，不能者止。",
  startDate: "12/23/2015 00:00:00",
  showBeian: ${demoConfig.footer.showBeian}, // 是否显示备案号
  beianNumber: "${demoConfig.footer.beianNumber}", // 备案号
  beianUrl: "https://beian.miit.gov.cn/" // 备案查询链接
}`}</code></pre>
      </div>
      
      <div class="usage-tips">
        <h3>使用说明:</h3>
        <ol>
          <li><strong>开启备案号:</strong> 将 <code>showBeian</code> 设置为 <code>true</code></li>
          <li><strong>设置备案号:</strong> 在 <code>beianNumber</code> 中填入您的备案号</li>
          <li><strong>备案查询:</strong> 点击备案号会跳转到工信部备案查询网站</li>
          <li><strong>版权年份:</strong> 自动计算从起始年份到当前年份</li>
        </ol>
      </div>
    </section>
    
    <!-- 返回主页 -->
    <section class="navigation">
      <a href="/" class="btn">
        <i class="fa-solid fa-home"></i> 返回主页
      </a>
    </section>
  </div>
</div>

<style>
  .demo-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    font-family: 'Microsoft Yahei', Arial, sans-serif;
    padding: 40px 20px;
  }
  
  .demo-container {
    max-width: 1000px;
    margin: 0 auto;
  }
  
  h1 {
    text-align: center;
    font-size: 42px;
    margin-bottom: 40px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  
  section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #64b5f6;
  }
  
  h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #81c784;
  }
  
  /* 控制面板样式 */
  .control-group {
    margin-bottom: 20px;
  }
  
  .switch-label {
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    font-size: 16px;
  }
  
  .switch-label input[type="checkbox"] {
    display: none;
  }
  
  .switch-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    transition: all 0.3s ease;
  }
  
  .switch-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
  }
  
  .switch-label input:checked + .switch-slider {
    background: #64b5f6;
  }
  
  .switch-label input:checked + .switch-slider::before {
    transform: translateX(26px);
  }
  
  .beian-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    margin-top: 8px;
  }
  
  .beian-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  .status-info ul {
    list-style: none;
    padding: 0;
  }
  
  .status-info li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* 页脚预览样式 */
  .preview-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 40px 20px;
    margin-top: 20px;
  }
  
  .demo-footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
  }
  
  .motto-text {
    font-size: 18px;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    letter-spacing: 1px;
  }
  
  .time-counter {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 16px 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  
  .counter-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
  }
  
  .counter-display {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    color: #fff;
    font-weight: 600;
    letter-spacing: 2px;
  }
  
  .beian-info a {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
  }
  
  .beian-info a:hover {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .copyright-years {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 10px;
    width: 100%;
    max-width: 400px;
  }
  
  /* 配置信息样式 */
  .config-example {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
  }
  
  pre {
    margin: 0;
    overflow-x: auto;
  }
  
  code {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: #e8f5e8;
  }
  
  .usage-tips ol {
    padding-left: 20px;
  }
  
  .usage-tips li {
    margin-bottom: 10px;
    line-height: 1.5;
  }
  
  .usage-tips code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
  }
  
  /* 导航按钮 */
  .navigation {
    text-align: center;
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #64b5f6;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .btn:hover {
    background: #42a5f5;
    transform: translateY(-2px);
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .demo-page {
      padding: 20px 10px;
    }
    
    h1 {
      font-size: 32px;
    }
    
    section {
      padding: 20px;
    }
    
    .beian-input {
      font-size: 16px; /* 防止iOS缩放 */
    }
  }
</style>
