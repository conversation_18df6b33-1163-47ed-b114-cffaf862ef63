// 网站配置文件
export const siteConfig = {
  // 基本信息
  title: "Cool's Home Page",
  description: "Cool's Homepage",
  keywords: "<PERSON>",
  author: "<PERSON>",

  // Logo 配置
  logo: {
    src: "/images/logo.gif",
    alt: "Logo",
    href: "https://blog.nmslwsnd.com",
    target: "_blank"
  },

  // 背景配置
  background: {
    image: "/images/partical-bg.webp",
    fallbackColor: "linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)",
    overlay: "rgba(0, 0, 0, 0.6)"
  },

  // 多语言欢迎信息
  welcomeMessages: [
    { title: 'Hello', subtitle: 'Welcome to Cool\'s Personal Pages!' },
    { title: '你好', subtitle: '欢迎来到 Cool 的个人主页！' },
    { title: 'Bonjour', subtitle: 'C\'est ma page d\'accueil.' },
    { title: '<PERSON><PERSON>', subtitle: 'Das ist meine Persönliche homepage.' },
    { title: 'こんにちは', subtitle: 'これは私の個人のホームページです.' },
    { title: 'Ciao', subtitle: 'Questa è la mia Pagina personale.' },
    { title: 'Привет', subtitle: 'Это моя личная страница .' }
  ],

  // 社交链接
  socialLinks: [
    {
      icon: 'fa-user',
      href: 'https://blog.nmslwsnd.com',
      title: 'Blog',
      description: '个人博客'
    },
    {
      icon: 'fa-cloud',
      href: 'https://nextcloud.nmslwsnd.com',
      title: 'NextCloud',
      description: '云存储'
    },
    {
      icon: 'fa-steam',
      href: 'https://steamcommunity.com/id/hello-world-',
      title: 'Steam',
      description: 'Steam 个人资料'
    },
    {
      icon: 'fa-play-circle',
      href: 'https://space.bilibili.com/your-bilibili-id',
      title: 'Bilibili',
      description: 'B站主页'
    }
  ],

  // 页脚配置
  footer: {
    copyrightText: "陈力就列，不能者止。",
    startDate: "12/23/2015 00:00:00" // 计时器开始时间
  },

  // 粒子动画配置
  particles: {
    count: 80,
    color: '#ffffff',
    opacity: 0.5,
    size: 3,
    speed: 0.5,
    lineDistance: 150,
    lineOpacity: 0.4
  },

  // 轮播配置
  carousel: {
    autoPlay: true,
    interval: 4000, // 4秒切换一次
    showDots: true,
    showArrows: true
  },

  // 动画配置
  animations: {
    pageLoadDelay: 800, // 页面加载动画持续时间
    fadeInDelay: {
      logo: 0.5,
      carousel: 0.7,
      social: 0.9,
      footer: 1.0
    }
  }
};

// 主题配置
export const themeConfig = {
  colors: {
    primary: '#ffffff',
    secondary: '#00ad9f',
    background: '#000000',
    overlay: 'rgba(0, 0, 0, 0.6)',
    text: {
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.8)',
      muted: 'rgba(255, 255, 255, 0.6)'
    }
  },

  fonts: {
    primary: "'Microsoft Yahei', 'Roboto', sans-serif",
    monospace: "'Courier New', monospace"
  },

  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
    large: '1200px'
  }
};

// 响应式配置
export const responsiveConfig = {
  mobile: {
    container: {
      maxWidth: '320px',
      padding: '15px 10px'
    },
    logo: {
      width: '100px',
      marginBottom: '30px'
    },
    carousel: {
      height: '80px',
      titleSize: '28px',
      subtitleSize: '14px'
    },
    social: {
      iconSize: '45px',
      fontSize: '18px',
      gap: '12px'
    }
  },

  tablet: {
    container: {
      maxWidth: '600px',
      padding: '20px 15px'
    },
    logo: {
      width: '120px',
      marginBottom: '40px'
    },
    carousel: {
      height: '100px',
      titleSize: '36px',
      subtitleSize: '16px'
    },
    social: {
      iconSize: '50px',
      fontSize: '20px',
      gap: '15px'
    }
  },

  desktop: {
    container: {
      maxWidth: '1170px',
      padding: '40px 20px'
    },
    logo: {
      width: '150px',
      marginBottom: '60px'
    },
    carousel: {
      height: '120px',
      titleSize: '48px',
      subtitleSize: '18px'
    },
    social: {
      iconSize: '60px',
      fontSize: '24px',
      gap: '20px'
    }
  }
};
